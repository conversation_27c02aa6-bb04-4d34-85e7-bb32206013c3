#!/usr/bin/env python3
"""
Email Marketing SQL Agent with LangGraph

This script implements an SQL agent that uses <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>rap<PERSON> with Groq LLM
to translate natural language queries into SQL for email marketing campaign analysis.
"""

# ----- 1. Import Libraries -----
import os
import sqlite3
import json
import yaml
import traceback
from dotenv import load_dotenv
from typing import Annotated, Literal, Any, Dict, List, Optional, Union

# LangChain & LangGraph Imports
from langchain_groq import ChatGroq
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_community.utilities import SQLDatabase 
from langchain_core.messages import AIMessage, AnyMessage, ToolMessage, HumanMessage
from langchain_core.runnables import RunnableLambda, RunnableWithFallbacks
from langchain_community.agent_toolkits import SQLDatabaseToolkit

# Pydantic & LangGraph
from pydantic import BaseModel, Field
from typing_extensions import TypedDict
from langgraph.graph import END, StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode

# Optional visualization imports
try:
    from IPython.display import Image, display
    from langchain_core.runnables.graph import MermaidDrawMethod
    VISUALIZATION_AVAILABLE = True
except ImportError:
    VISUALIZATION_AVAILABLE = False

# ----- 2. Configuration Loading -----

# Load environment variables
load_dotenv()

# Load prompts and SQL queries from config files
def load_config():
    """Load prompts and SQL queries from the config files"""
    config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
    
    # Load prompts
    prompts_path = os.path.join(config_dir, 'prompts.yml')
    try:
        with open(prompts_path, 'r', encoding='utf-8') as file:
            prompts_config = yaml.safe_load(file)
            prompts = prompts_config['sql_agent']
    except FileNotFoundError:
        print(f"Prompts config file not found at: {prompts_path}")
        raise
    except KeyError:
        print("sql_agent section not found in prompts config")
        raise
    
    # Load SQL queries
    queries_path = os.path.join(config_dir, 'sql_queries.yml')
    try:
        with open(queries_path, 'r', encoding='utf-8') as file:
            queries_config = yaml.safe_load(file)
    except FileNotFoundError:
        print(f"SQL queries config file not found at: {queries_path}")
        raise
    
    return prompts, queries_config

# Load configuration before any usage
prompts, sql_queries = load_config()
print("Prompts and SQL queries loaded from config files.")
DB_NAME = "email_marketing.db"
connection = sqlite3.connect(DB_NAME)
cursor = connection.cursor()
print(f"Connected to new database: {DB_NAME}")

# ----- 3. LLM Initialization -----
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GROQ_API_KEY:
    print("GROQ_API_KEY not found. Please set it in your .env file or environment.")
    raise ValueError("GROQ_API_KEY environment variable is not set")
else:
    os.environ["GROQ_API_KEY"] = GROQ_API_KEY
    print("GROQ_API_KEY loaded.")

llm = ChatGroq(model="llama3-70b-8192")
print("ChatGroq LLM initialized.")

# ----- 4. Database Setup -----
DB_NAME = "email_marketing.db"

# ----- 5. SQLDatabase Connection & Toolkit -----
db = SQLDatabase.from_uri(f"sqlite:///{DB_NAME}")

print("Dialect:", db.dialect)
print("Usable tables:", db.get_usable_table_names())
print("Sample rows from campaign_data:", db.run("SELECT * FROM campaign_data LIMIT 2"))

# ----- 6. LangGraph Agent Definition -----

# --- 6.1 Define Tools ---
# Standard SQL tools from toolkit
toolkit = SQLDatabaseToolkit(db=db, llm=llm)
sql_tools = toolkit.get_tools() # This gives query, schema, list_tables, query_checker

# Extract specific tools we'll use in the graph if needed by name, or use them as a list
list_tables_tool = next((t for t in sql_tools if t.name == "sql_db_list_tables"), None)
get_schema_tool = next((t for t in sql_tools if t.name == "sql_db_schema"), None)

# Custom tool for executing queries, ensuring it doesn't throw an error to stop the graph
@tool
def db_query_tool(query: str) -> str:
    """
    Execute a SQL query against the database and return the result.
    If the query is invalid or returns no result, an error message or 'No results found.' will be returned.
    In case of an error, the user is advised to rewrite the query and try again.
    """
    result = db.run_no_throw(query)
    if result is None: # db.run_no_throw might return None on error
        return "Error: Query failed. Please check the SQL syntax and table/column names, then rewrite your query and try again."
    if not result: # Empty list or empty string for no results
        return "Query executed successfully, but no results were found for your criteria."
    return result

# Pydantic model for submitting the final answer
class SubmitFinalAnswer(BaseModel):
    """Submit the final answer to the user based on the query results."""
    final_answer: str = Field(..., description="The final natural language answer to the user's question, based on the SQL query results.")

print("Tools defined.")
print(f"List tables tool: {list_tables_tool.name if list_tables_tool else 'Not found'}")
print(f"Get schema tool: {get_schema_tool.name if get_schema_tool else 'Not found'}")
print(f"DB query tool: {db_query_tool.name}")

# #### 4.2. Define State

class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    # Optional: to explicitly track errors if needed beyond tool message content
    error: str | None 

print("State defined.")

# #### 4.3. Define Prompts and LLM Bindings
query_check_system = prompts['query_check_system']

query_check_prompt = ChatPromptTemplate.from_messages([
    ("system", query_check_system),
    ("placeholder", "{messages}")
])

query_check = query_check_prompt | llm.bind_tools([db_query_tool])

print("Query check prompt and LLM binding defined.")

query_gen_system = prompts['query_generation_system']

query_gen_prompt = ChatPromptTemplate.from_messages([
    ("system", query_gen_system),
    ("placeholder", "{messages}")
])

query_gen = query_gen_prompt | llm.bind_tools([SubmitFinalAnswer])
print("Query generation prompt and LLM binding defined.")

# #### 4.4. Define Nodes and Edges

def first_tool_call(state: State) -> dict[str, list[AIMessage]]:
    # This node prepares the initial call to list tables to inform the LLM about the DB structure.
    return {"messages": [AIMessage(content="", tool_calls=[{"name": "sql_db_list_tables", "args": {}, "id": "tool_list_tables"}])]}

def handle_tool_error(state: State) -> dict:
    error = state.get("error")
    # Ensure messages is a list and the last message exists and has tool_calls
    messages = state.get("messages", [])
    if not messages or not hasattr(messages[-1], 'tool_calls') or not messages[-1].tool_calls:
        # Fallback if there's an error but no tool_calls to attribute it to (should be rare)
        return {"messages": [AIMessage(content=f"An unexpected error occurred: {repr(error)}. Please try rephrasing your request.")]}
    
    tool_calls = messages[-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"Error: Tool call failed with error: {repr(error)}. Please analyze the error and fix your approach.",
                tool_call_id=tc["id"],
            )
            for tc in tool_calls
        ]
    }

def create_tool_node_with_fallback(tools: list) -> RunnableWithFallbacks[Any, dict]:
    return ToolNode(tools).with_fallbacks(
        [RunnableLambda(handle_tool_error)], exception_key="error"
    )



# ... (inside query_gen_node)
def query_gen_node(state: State):
    # ... (existing code) ...
    print(f"--- Entering query_gen_node ---")
    current_messages = state["messages"]
    response_message = query_gen.invoke({"messages": current_messages})
    print(f"query_gen_node initial LLM response_message: {response_message}")

    tool_error_messages = []

    # Attempt to fix LLM outputting tool call as string content
    if not response_message.tool_calls and response_message.content:
        try:
            # Check if content is a JSON string that looks like a tool call
            content_as_json = json.loads(response_message.content)
            if isinstance(content_as_json, dict) and "tool_calls" in content_as_json:
                # Attempt to reconstruct the message with proper tool_calls
                parsed_tool_calls = content_as_json["tool_calls"]
                # Basic validation of the parsed structure
                if isinstance(parsed_tool_calls, list) and \
                   all(isinstance(tc, dict) and "name" in tc and "args" in tc and "id" in tc for tc in parsed_tool_calls):
                    print(f"query_gen_node: Detected tool call in content, attempting to parse and fix.")
                    response_message.tool_calls = parsed_tool_calls
                    response_message.content = "" # Clear content if it was just the tool call
                    print(f"query_gen_node: Fixed response_message: {response_message}")
                else:
                    print(f"query_gen_node: Content looked like JSON tool_calls but structure was invalid.")
            elif isinstance(content_as_json, dict) and "function" in content_as_json and "parameters" in content_as_json and content_as_json.get("function", {}).get("name") == "SubmitFinalAnswer":
                # Handle cases where the LLM might output a single tool call structure directly as JSON content
                print(f"query_gen_node: Detected single tool call (SubmitFinalAnswer) in content, attempting to parse and fix.")
                tool_call_args = content_as_json["parameters"]
                tool_call_name = content_as_json["function"]["name"]
                response_message.tool_calls = [{"name": tool_call_name, "args": tool_call_args, "id": "parsed_from_content"}]
                response_message.content = "" # Clear content
                print(f"query_gen_node: Fixed response_message for single tool_call: {response_message}")

        except json.JSONDecodeError:
            # Content is not JSON, or not the specific JSON structure we're looking for
            pass # Proceed as normal

    if response_message.tool_calls:
        for tc in response_message.tool_calls:
            if tc["name"] != "SubmitFinalAnswer":
                tool_error_messages.append(
                    ToolMessage(
                        content=f"Error: You called the wrong tool: {tc['name']}. You should only call SubmitFinalAnswer to give the final response, or output a plain SQL query. Please regenerate the SQL query as plain text.",
                        tool_call_id=tc["id"],
                    )
                )
    
    final_messages_to_add = [response_message] + tool_error_messages
    print(f"--- Exiting query_gen_node (adding {len(final_messages_to_add)} messages) ---")
    return {"messages": final_messages_to_add}


def model_check_query_node(state: State) -> dict[str, list[AIMessage]]:
    """Node that calls the LLM to check/correct a generated SQL query before execution."""
    # The last message should be the plain text SQL query generated by query_gen_node
    print("--- Entering model_check_query_node ---")
    # print(f"Current state messages for model_check_query: {state['messages'][-3:]}")
    
    # The query to check is expected to be the content of the last AIMessage if it's not a tool_call
    # This logic assumes query_gen_node produces an AIMessage with the SQL query as its `content`.
    # And that message should NOT have tool_calls (unless it was a mistake handled by query_gen_node)
    if state["messages"][-1].tool_calls: # Should not happen if query_gen is correct
        print("Error in model_check_query_node: Expected plain query, got tool call.")
        # This is a graph logic error, pass it through to let the graph handle it or error out
        return {"messages": [AIMessage(content="Graph Logic Error: model_check_query_node received unexpected tool call.")]}

    # query_check is LLM bound with db_query_tool
    # We pass the last message (which should contain the raw SQL from query_gen) as a HumanMessage to fit query_check_prompt
    raw_sql_query_message = HumanMessage(content=state["messages"][-1].content)
    checked_query_response = query_check.invoke({"messages": [raw_sql_query_message]})
    print(f"model_check_query_node response: {checked_query_response}")
    print("--- Exiting model_check_query_node ---")
    return {"messages": [checked_query_response]}

def should_continue(state: State) -> Literal[END, "model_check_query_node", "query_gen_node"]:
    print("--- Deciding in should_continue ---")
    messages = state["messages"]
    last_message = messages[-1]
    # print(f"Last message for should_continue: {last_message}")

    if isinstance(last_message, ToolMessage) and "Error: Tool call failed" in last_message.content:
        print("Decision: Tool execution error, go back to query_gen_node to retry.")
        return "query_gen_node"
    
    if isinstance(last_message, ToolMessage) and "Query executed successfully, but no results were found" in last_message.content:
        print("Decision: Query returned no results, go to query_gen_node for interpretation.")
        return "query_gen_node"

    if getattr(last_message, "tool_calls", None):
        is_final_answer = any(tc['name'] == 'SubmitFinalAnswer' for tc in last_message.tool_calls)
        if is_final_answer:
            print("Decision: SubmitFinalAnswer called via tool_calls, END.")
            return END
        else: # LLM called a tool other than SubmitFinalAnswer (e.g. db_query_tool directly from query_gen)
              # This can happen if query_gen_node's correction logic above wasn't triggered
              # or if the LLM in query_gen decided to call db_query_tool itself.
            print(f"Decision: LLM called unexpected tool '{last_message.tool_calls[0]['name']}' from query_gen, go to query_gen_node to correct.")
            # We want query_gen_node to see this problematic tool call and add an error message.
            return "query_gen_node" # This might be redundant if query_gen_node already added error, but safe.

    if isinstance(last_message, AIMessage) and not last_message.tool_calls and last_message.content:
        # Check if the content is likely an SQL query
        content_lower = last_message.content.lower()
        # Simple check for SQL keywords, can be made more robust
        is_sql_like = any(keyword in content_lower for keyword in ["select ", "insert ", "update ", "delete ", "create ", "drop ", "alter "])
        
        if is_sql_like and "submitfinalanswer" not in content_lower: # Further check to avoid misinterpreting stringified tool calls
            print("Decision: Plain SQL query generated, go to model_check_query_node.")
            return "model_check_query_node"
        else:
            # Content is present but not SQL-like, or might be a stringified SubmitFinalAnswer
            # Let query_gen_node try to parse it or handle it.
            print("Decision: AIMessage with content, but not clearly SQL or already handled as tool_call. Route to query_gen_node.")
            return "query_gen_node"
            
    print("Decision: Default or tool result (not an error, not no results), go to query_gen_node for interpretation/retry.")
    return "query_gen_node"

print("Node and edge logic functions defined.")

# #### 4.5. Construct the Graph

workflow = StateGraph(State)

# Add nodes
workflow.add_node("first_tool_call_node", first_tool_call) # Kicks off by listing tables
workflow.add_node("list_tables_tool_node", create_tool_node_with_fallback([list_tables_tool]))

# Node to prompt LLM to call get_schema_tool based on listed tables
model_get_schema_prompt = ChatPromptTemplate.from_messages([
    ("system", "Based on the following list of tables, identify all relevant tables for answering typical email marketing analysis questions. Then, call the 'sql_db_schema' tool with a comma-separated list of these table names to get their schema. Tables: {table_list}"),
    ("placeholder", "{messages}") # To carry over the ToolMessage from list_tables_tool
])
model_get_schema = model_get_schema_prompt | llm.bind_tools([get_schema_tool])
workflow.add_node("model_get_schema_node", 
                  lambda state: {"messages": [model_get_schema.invoke({"messages": state["messages"], 
                                                                       "table_list": state["messages"][-1].content})]} # Get table list from previous tool message
                 )

workflow.add_node("get_schema_tool_node", create_tool_node_with_fallback([get_schema_tool]))
workflow.add_node("query_gen_node", query_gen_node) # Generates SQL or final answer
workflow.add_node("model_check_query_node", model_check_query_node) # LLM checks/corrects SQL
workflow.add_node("execute_query_tool_node", create_tool_node_with_fallback([db_query_tool])) # Executes corrected SQL
# workflow.add_node("final_answer_node", create_tool_node_with_fallback([SubmitFinalAnswer])) # Dummy node for SubmitFinalAnswer if it were a real tool call for graph to hit END

# Define edges
workflow.add_edge(START, "first_tool_call_node")
workflow.add_edge("first_tool_call_node", "list_tables_tool_node")
workflow.add_edge("list_tables_tool_node", "model_get_schema_node") 
workflow.add_edge("model_get_schema_node", "get_schema_tool_node")
workflow.add_edge("get_schema_tool_node", "query_gen_node") # Schema info goes to query_gen

workflow.add_conditional_edges(
    "query_gen_node",
    should_continue,
    {
        "model_check_query_node": "model_check_query_node",
        "query_gen_node": "query_gen_node", # Loop back if error or needs more processing
        END: END 
    }
)

workflow.add_edge("model_check_query_node", "execute_query_tool_node") # Checked query goes for execution
workflow.add_edge("execute_query_tool_node", "query_gen_node") # Results/errors of execution go back to query_gen for final answer formulation or retry

# Compile the graph
app = workflow.compile()
print("LangGraph workflow compiled.")

# #### 4.6. Visualize the Graph (Optional)

try:
    display(
        Image(
            app.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API, # Requires internet and API access for mermaid.ink
                # Or use MermaidDrawMethod.PYPPETEER if you have pyppeteer installed
            )
        )
    )
except Exception as e:
    print(f"Could not display graph: {e}. Ensure playwright is installed for local rendering or use API method.")
    # To install playwright for local rendering: 
    # pip install playwright
    # playwright install

# ### 5. Running the Agent
def run_query(question):
    print(f"\n--- Running query for: '{question}' ---")
    query_input = {"messages": [("user", question)]}
    response = None
    try:
        for event in app.stream(query_input, {"recursion_limit": 25}): # Added recursion limit
            # print(f"Event: {event}") # Detailed event logging
            last_message_key = list(event.keys())[-1] # Get the key of the node that produced the event
            # print(f"Node '{last_message_key}' output: {event[last_message_key]}")
            if END in event: # Check if the END node is in the event keys
                response = event[END]
                break
            elif isinstance(event, dict) and event.get(list(event.keys())[-1]) and event.get(list(event.keys())[-1]).get('messages'):
                 response = event.get(list(event.keys())[-1]) # Keep the last relevant message set
            else:
                 response = event # Fallback

        if response and response.get("messages"):
            final_ai_message = response["messages"][-1]
            # print(f"Final AI Message object: {final_ai_message}")
            if final_ai_message.tool_calls and final_ai_message.tool_calls[0]["name"] == "SubmitFinalAnswer":
                final_answer = final_ai_message.tool_calls[0]["args"]["final_answer"]
                print(f"\nFinal Answer: {final_answer}")
                return final_answer
            else:
                print("\nAgent did not call SubmitFinalAnswer. Last message:")
                print(final_ai_message.content if hasattr(final_ai_message, 'content') else final_ai_message)
                return "Agent did not provide a final answer through SubmitFinalAnswer."
        else:
            print("\nNo final response messages found or graph ended unexpectedly.")
            # print(f"Last known response object: {response}")
            return "No response from agent."
    except Exception as e:
        print(f"An error occurred during agent execution: {e}")
        import traceback
        traceback.print_exc()
        return f"Error: {e}"

# Test Queries
questions = [
    # "What is the best performing subject line by Open Rate?",
    # "Give me the top 2 email copies by PCR (Click-Through Rate). Include subject and body.",
    # "Which sender has the highest average Click-Through Rate (PCR)?",
    # "How many mails were delivered today (assuming today is 2024-07-04)?", # LLM should use the date provided
    "Give me top 3 insights based on the campaign data, considering Open Rate and PCR."
]

for q in questions:
    run_query(q)
    print("--------------------------------------------------")

# Example of a more complex question that might require iteration or clarification
# run_query("Compare the performance of 'Sales Team' emails versus 'News Team' emails. Provide key metrics like total sent, average OR, and average PCR.")

# Close the database connection when done (optional in scripts, good practice)
connection.close()

