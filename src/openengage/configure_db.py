# ----- 1. Import Libraries -----
import os
import sqlite3
import json
import yaml
import traceback
from dotenv import load_dotenv
from typing import Annotated, Literal, Any, Dict, List, Optional, Union


"""
This script is used to configure the database for the OpenEngage application.
It creates a new database file and populates it with sample data.
"""

# Load environment variables
load_dotenv()

# Load prompts and SQL queries from config files
def load_config():
    """Load prompts and SQL queries from the config files"""
    config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
    
    # Load prompts
    prompts_path = os.path.join(config_dir, 'prompts.yml')
    try:
        with open(prompts_path, 'r', encoding='utf-8') as file:
            prompts_config = yaml.safe_load(file)
            prompts = prompts_config['sql_agent']
    except FileNotFoundError:
        print(f"Prompts config file not found at: {prompts_path}")
        raise
    except KeyError:
        print("sql_agent section not found in prompts config")
        raise
    
    # Load SQL queries
    queries_path = os.path.join(config_dir, 'sql_queries.yml')
    try:
        with open(queries_path, 'r', encoding='utf-8') as file:
            queries_config = yaml.safe_load(file)
    except FileNotFoundError:
        print(f"SQL queries config file not found at: {queries_path}")
        raise
    
    return prompts, queries_config

# Load configuration before any usage
prompts, sql_queries = load_config()
print("Prompts and SQL queries loaded from config files.")



# ----- 4. Database Setup -----
DB_NAME = "email_marketing.db"

# Remove the old database file if it exists, for a clean run each time during development
if os.path.exists(DB_NAME):
    os.remove(DB_NAME)
    print(f"Removed old database: {DB_NAME}")

connection = sqlite3.connect(DB_NAME)
cursor = connection.cursor()
print(f"Connected to new database: {DB_NAME}")

# --- Phase 1: Create Table ---
table_creation_query = sql_queries['database_setup']['create_table']
cursor.execute(table_creation_query)
connection.commit() # Commit table creation
print("Table 'campaign_data' created or already exists.")

# --- Phase 2: Create View ---
view_creation_query = sql_queries['database_setup']['create_view']
cursor.execute(view_creation_query)
connection.commit() # Commit view creation
print("View 'campaign_performance' created or already exists.")
    
# --- Phase 3: Insert Sample Data ---
sample_campaign_data = sql_queries['sample_data']
insert_query = sql_queries['database_setup']['insert_sample_data']
cursor.executemany(insert_query, sample_campaign_data)
connection.commit()
print(f"Inserted {len(sample_campaign_data)} sample rows into 'campaign_data'.")

# --- Phase 4: Verify Data Insertion ---
cursor.execute(sql_queries['verification']['select_sample'])
for row in cursor.fetchall():
    print(row)

cursor.execute(sql_queries['verification']['count_rows'])
print(f"Total rows in campaign_data: {cursor.fetchone()[0]}")

connection.close()