"""
Product editor UI component for OpenEngage.
"""
import json
import uuid
import streamlit as st
import pandas as pd
from utils.file_utils import load_user_journey, save_user_journey, get_all_products, get_default_journey

def display_product_editor():
    """Display the product data editor interface"""
    st.write("### Product Details")

    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Load all products to create a dropdown with optional filtering
    all_products = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)

    # If filtering is enabled and no products found, show a message
    if org_filter_enabled and org_url and not all_products:
        st.warning(f"No products found for your organization ({org_url}). Showing all products instead.")
        # Fall back to showing all products
        all_products = get_all_products(filter_by_org=False)

    # Get current product data - first try crew manager, then session state
    data = None
    if hasattr(st.session_state.crew, 'product_data') and st.session_state.crew.product_data:
        data = st.session_state.crew.product_data
    elif hasattr(st.session_state, 'product_data') and st.session_state.product_data:
        data = st.session_state.product_data

    # If no product data is available, check if we have existing products
    if not data and hasattr(st.session_state, 'existing_products') and st.session_state.existing_products:
        data = st.session_state.existing_products[0]
        # Update session state
        st.session_state.product_data = data
        if hasattr(st.session_state.crew, 'product_data'):
            st.session_state.crew.product_data = data

    # Always show product selection dropdown
    current_product_name = data.get("Product_Name", "") if data else ""

    # Get product names from all_products
    product_names = [p.get("Product_Name", "") for p in all_products]

    # Add an option to create a new product if needed
    if current_product_name and current_product_name not in product_names:
        product_names.append(current_product_name)

    # Show the dropdown if there are any products
    if product_names:
        # Set default index
        default_index = 0
        if current_product_name in product_names:
            default_index = product_names.index(current_product_name)

        selected_product = st.selectbox(
            "Select a product to edit:",
            options=product_names,
            index=default_index
        )

        # If user selected a different product, update the data
        if selected_product != current_product_name:
            # Find the selected product data
            for product in all_products:
                if product.get("Product_Name") == selected_product:
                    data = product
                    # Update session state
                    st.session_state.product_data = data
                    if hasattr(st.session_state.crew, 'product_data'):
                        st.session_state.crew.product_data = data
                    st.rerun()

    st.write("Review and edit the information below as needed:")

    if not data:
        st.error("No product data available. Please add product URLs in the chat.")

        # Add a message to the chat asking for product URLs
        if "messages" in st.session_state:
            product_url_message = "I don't see any products in your database. Please add the product URLs separated by commas so I can analyze them for you."
            if not any(product_url_message in msg.get("content", "") for msg in st.session_state.messages):
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": product_url_message
                })

        # Switch back to chat view
        st.session_state.show_product_editor = False
        st.session_state.show_org_editor = False
        st.rerun()
        return

    st.session_state.show_collaterals_section = True

    # Create tabs for different sections
    basic_tab, features_tab, summary_tab = st.tabs([
        "Basic Information", "Product Features", "Product Summary"
    ])

    with basic_tab:
        edited_basic = st.data_editor(
            {
                "Product_URL": data.get("Product_URL", ""),
                "Product_Name": data.get("Product_Name", ""),
                "Company_Name": data.get("Company_Name", ""),
                "Company_URL": data.get("Company_URL", st.session_state.organization_url),
                "Type_of_Product": data.get("Type_of_Product", "")
            },
            key="basic_info",
            num_rows="fixed"
        )

    with features_tab:
        features = data.get("Product_Features", [])
        edited_features = st.data_editor(
            [{"feature": f} for f in features],
            key="features",
            num_rows="dynamic",
            column_config={
                "feature": "Feature Description"
            }
        )

    with summary_tab:
        edited_summary = st.text_area(
            "Product Summary",
            value=data.get("Product_Summary", ""),
            key="summary",
            height=200
        )

    # Save button
    if st.button("Save Product Details", type="primary"):
        # Get the current product data
        updated_data = {
            "Product_URL": edited_basic["Product_URL"],
            "Product_Name": edited_basic["Product_Name"],
            "Company_Name": edited_basic["Company_Name"],
            "Company_URL": edited_basic["Company_URL"],
            "organization_url": edited_basic["Company_URL"],  # Add organization_url field
            "Type_of_Product": edited_basic["Type_of_Product"],
            "Product_Features": [item["feature"] for item in edited_features],
            "Product_Summary": edited_summary
        }

        # Update the product data in both places
        st.session_state.product_data = updated_data
        st.session_state.crew.product_data = updated_data

        # Save to file
        try:
            # Load existing products
            try:
                with open("data/product_details.json", "r") as f:
                    products = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                products = []

            # Check if product already exists
            product_exists = False
            for i, product in enumerate(products):
                if product.get("Product_Name") == updated_data["Product_Name"]:
                    products[i] = updated_data
                    product_exists = True
                    break

            if not product_exists:
                products.append(updated_data)

            # Save updated products list
            with open("data/product_details.json", "w") as f:
                json.dump(products, f, indent=4)

            # Also update crew.products_list if it exists
            if hasattr(st.session_state.crew, 'products_list'):
                # Check if product exists in products_list
                product_exists = False
                for i, product in enumerate(st.session_state.crew.products_list):
                    if product.get("Product_Name") == updated_data["Product_Name"]:
                        st.session_state.crew.products_list[i] = updated_data
                        product_exists = True
                        break

                if not product_exists:
                    st.session_state.crew.products_list.append(updated_data)

            # Explicitly call save method of crew manager to ensure synchronization
            if hasattr(st.session_state.crew, 'save_product_data'):
                st.session_state.crew.save_product_data()

            st.success("✅ Product details saved successfully!")
            st.rerun()

        except Exception as e:
            st.error(f"Failed to save product details: {str(e)}")

    # Collateral Management Section
    st.write("---")
    product_name = data.get("Product Name", data.get("Product_Name", ""))
    product_url = data.get("Product URL", data.get("Product_URL", ""))

    # Create a unique key for this product's collaterals
    product_key = f"collaterals_{product_url}"

    if st.session_state.show_collaterals_section:
        has_collaterals = st.radio(f"Do you have any collaterals for {product_name}?", ["No", "Yes"], key=f"has_collaterals_{product_url}")

        if has_collaterals == "Yes":
            # Initialize product-specific collaterals if not already done
            if "product_collaterals" not in st.session_state:
                st.session_state.product_collaterals = {}

            # Initialize collaterals for this specific product if not already done
            if product_key not in st.session_state.product_collaterals:
                st.session_state.product_collaterals[product_key] = []

            # Get collaterals directly from the product data
            current_collaterals = data.get("Collaterals", [])

            if current_collaterals:
                st.write("### Uploaded Collaterals")

                try:
                    # Convert collaterals data for display
                    collateral_data = []
                    for collateral in current_collaterals:
                        # Format features and marketing points as bullet points
                        features = "\n• " + "\n• ".join(collateral.get("features", [])) if collateral.get("features") else ""
                        marketing_points = "\n• " + "\n• ".join(collateral.get("marketing_points", [])) if collateral.get("marketing_points") else ""

                        collateral_data.append({
                            "Collateral Name": collateral["name"],
                            "Summary": collateral.get("summary", ""),
                            "Features": features,
                            "Marketing Points": marketing_points
                        })

                    # Create and display the DataFrame with styling
                    st.markdown("""
                        <style>
                        .stDataFrame {
                            font-size: 14px;
                        }
                        .stDataFrame td {
                            white-space: pre-wrap !important;
                            padding: 8px !important;
                        }
                        </style>
                    """, unsafe_allow_html=True)

                    collateral_df = pd.DataFrame(collateral_data)
                    st.dataframe(
                        collateral_df,
                        use_container_width=True,
                        column_config={
                            "Collateral Name": st.column_config.TextColumn(
                                "Collateral Name",
                                width=150,
                                help="Name of the uploaded collateral"
                            ),
                            "Summary": st.column_config.TextColumn(
                                "Summary",
                                width=200,
                                help="AI-generated summary of the collateral"
                            ),
                            "Features": st.column_config.TextColumn(
                                "Features",
                                width=200,
                                help="Features mentioned in the collateral"
                            ),
                            "Marketing Points": st.column_config.TextColumn(
                                "Marketing Points",
                                width=200,
                                help="Key aspects useful for marketing"
                            )
                        },
                        hide_index=True
                    )

                except Exception as e:
                    st.error(f"Error displaying collaterals: {str(e)}")
                    st.write("Please try refreshing the page or contact support if the issue persists.")

            # Add new collateral
            st.write("### Add New Collateral")

            # Initialize form key if not exists
            if "form_key" not in st.session_state:
                st.session_state.form_key = "default"

            def handle_form_submit():
                # Generate a new form key to force widgets to reset
                st.session_state.form_key = str(uuid.uuid4())

            # Create columns for the form
            col1, col2 = st.columns([2, 3])

            with col1:
                # Use dynamic key for text input
                collateral_name = st.text_input(
                    "Collateral Name",
                    key=f"new_collateral_name_{st.session_state.form_key}"
                )
            with col2:
                # Use dynamic key for file uploader
                uploaded_file = st.file_uploader(
                    "Upload Collateral",
                    key=f"collateral_uploader_{st.session_state.form_key}"
                )

            if uploaded_file is not None and collateral_name:
                # Save the file
                file_path = f"data/collaterals/{uploaded_file.name}"
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())

                # Process the collateral with CrewAI
                with st.spinner(f"Analyzing {collateral_name}... This may take a few moments."):
                    collateral_info = st.session_state.crew.analyze_collateral(file_path)

                # Create new collateral object
                new_collateral = {
                    "name": collateral_name,
                    "file_path": file_path,
                    "summary": collateral_info.get("summary", ""),
                    "features": collateral_info.get("features", []),
                    "marketing_points": collateral_info.get("marketing_points", [])
                }

                # Add to the current product data directly
                if "Collaterals" not in data:
                    data["Collaterals"] = []

                data["Collaterals"].append(new_collateral)

                # Update the product data in the crew manager
                # This will also save to file immediately
                st.session_state.crew.update_product_data(data)

                # Show success message
                st.success(f"Successfully analyzed {collateral_name}")

                # Reset form by changing the form key
                handle_form_submit()
                st.rerun()

        st.write("---")

    # Update button
    if st.button("Save Changes", type="secondary"):
        # Get the current collaterals from the product data
        current_collaterals = data.get("Collaterals", [])

        # Combine edited data
        updated_data = {
            "Product_URL": edited_basic["Product_URL"],
            "Product_Name": edited_basic["Product_Name"],
            "Company_Name": edited_basic["Company_Name"],
            "Company_URL": edited_basic["Company_URL"],
            "organization_url": edited_basic["Company_URL"],  # Add organization_url field
            "Type_of_Product": edited_basic["Type_of_Product"],
            "Product_Features": [item["feature"] for item in edited_features],
            "Product_Summary": edited_summary,
            "Collaterals": current_collaterals
        }

        # Update the stored data and save to file
        # The update_product_data method now handles saving to file
        st.session_state.crew.update_product_data(updated_data)

        # Load or create journey for this product using domain-specific stages
        product_name = updated_data["Product_Name"]

        # Get domain-specific default journey stages instead of using existing ones
        # This ensures we use the latest classification based on the organization's Class
        default_journey = get_default_journey()

        # Store the domain-specific journey for this product
        st.session_state.user_journey = default_journey

        # Save journey for this product
        save_user_journey(default_journey, product_name)

        st.success("Product details updated successfully!")

        # Add a message to the chat about user journey
        st.session_state.messages.append({
            "role": "assistant",
            "content": "Great! Now let's set up your user journey stages. This will help organize your marketing campaigns."
        })

        # Go directly to user journey (channel selection and communication settings are now in organization setup)
        st.session_state.show_collaterals_section = False
        st.session_state.show_channel_selection = False  # Ensure channel selection is skipped
        st.session_state.show_communication_settings = False  # Ensure communication settings are skipped
        st.session_state.show_user_journey = True
        st.rerun()

    # Channel selection has been moved to Organization Setup
    # If show_channel_selection is set, redirect to user journey
    if st.session_state.show_channel_selection:
        # Skip channel selection and go directly to user journey
        st.session_state.show_channel_selection = False
        st.session_state.show_user_journey = True

        # Add a message about user journey
        st.session_state.messages.append({
            "role": "assistant",
            "content": "Let's set up your user journey stages. This will help organize your marketing campaigns."
        })

        st.rerun()

    # Skip communication settings (now in organization setup)
    # Move directly to user journey if needed
    if st.session_state.show_communication_settings:
        # Hide settings form
        st.session_state.show_communication_settings = False
        st.session_state.show_user_journey = True
        st.rerun()

    # Show user journey if enabled
    if st.session_state.show_user_journey:
        st.write("---")
        
        # Get organization URL from session state if available
        org_url = None
        org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)
        if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
            org_url = st.session_state.current_user.get('organization', {}).get('url', None)
        
        # Load all products for dropdown
        all_products = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)
        
        # Get current product name
        current_product_name = st.session_state.crew.get_product_data().get('Product_Name', '')
        
        # Create row with columns for heading and product selection
        heading_col, dropdown_col = st.columns([2, 2])
        
        with heading_col:
            st.write("### User Journey")
            st.write("Please verify the user journey stages we have defined:")
            
        with dropdown_col:
            # Get product options excluding current product
            product_options = [p.get('Product_Name', '') for p in all_products if p.get('Product_Name', '') != current_product_name]
            
            # Create a checkbox for "All Products" option
            all_products_option = st.checkbox('Apply to all products', value=True, key='apply_journey_to_all')
            
            # Store selected products in a separate session state variable
            # that doesn't conflict with the widget keys
            if not all_products_option and product_options:
                # Display multiselect for specific products if not applying to all
                selected_products = st.multiselect(
                    'Select products:',
                    options=product_options,
                    key='selected_journey_products'
                )
                # We'll read directly from the widget keys instead of setting them
            else:
                # Just ensure we're not showing selected products when "All Products" is checked
                selected_products = []

        # Add options row with columns for Add Stage button and Cross-sell checkbox
        options_col1, options_col2 = st.columns([1, 2])
        
        with options_col1:
            # Add New Stage button
            add_stage_button = st.button("Add New Stage")
        
        with options_col2:
            # Cross sell Available checkbox
            cross_sell_available = st.checkbox('Cross sell Available', key='cross_sell_available')
            
            # Show Cool Off Period input if Cross sell is available
            if cross_sell_available:
                cool_off_period = st.number_input(
                    'Cool Off Period (days)',
                    min_value=1,
                    max_value=30,
                    value=7,
                    step=1,
                    key='cool_off_period'
                )
        
        # Create a DataFrame for the journey stages
        journey_data = st.session_state.user_journey.copy()
        
        # If cross sell is available and Cool Off Period stage doesn't exist, add it
        if cross_sell_available:
            # Check if Cool Off Period stage already exists
            cool_off_exists = any(stage.get('current_stage', '') == 'Cool Off Period' for stage in journey_data)
            
            if not cool_off_exists:
                # Add Cool Off Period stage with s_no and description
                # Generate the next s_no by finding the highest existing s_no and adding 1
                max_sno = 0
                for stage in journey_data:
                    if isinstance(stage.get('s_no'), (int, float)) and stage.get('s_no') > max_sno:
                        max_sno = stage.get('s_no')
                
                cool_off_stage = {
                    's_no': max_sno + 1,  # Set s_no to be one higher than the max existing s_no
                    'current_stage': 'Cool Off Period',
                    'description': f'Wait {cool_off_period} days before cross-selling to another product',
                    'days_to_wait': cool_off_period,
                    'is_cross_sell_stage': True  # Mark this as a cross-sell stage
                }
                journey_data.append(cool_off_stage)
                
                # Set the Goal Stage of the last regular stage to Cool Off Period
                regular_stages = [stage for stage in journey_data if not stage.get('is_cross_sell_stage', False)]
                if regular_stages:  # Make sure there's at least one regular stage
                    # Find the last regular stage based on s_no
                    last_regular_stage = max(regular_stages, key=lambda x: x.get('s_no', 0) if isinstance(x.get('s_no'), (int, float)) else 0)
                    # Update its goal_stage
                    last_regular_stage['goal_stage'] = 'Cool Off Period'
        else:
            # Remove Cool Off Period stage if it exists when checkbox is unchecked
            journey_data = [stage for stage in journey_data if not stage.get('is_cross_sell_stage', False)]
        
        journey_df = pd.DataFrame(journey_data)

        # Use Streamlit's data editor for inline editing
        edited_df = st.data_editor(
            journey_df,
            column_config={
                "s_no": "S. No.",
                "current_stage": "Current Stage",
                "description": "Description",
                "goal_stage": "Goal Stage"
            },
            num_rows="dynamic",  # Allow adding new rows
            key="journey_editor"
        )
        
        # Process Add New Stage button click
        if add_stage_button:
            new_stage = {
                "s_no": len(st.session_state.user_journey) + 1,
                "current_stage": "",
                "description": "",
                "goal_stage": ""
            }
            st.session_state.user_journey.append(new_stage)
            st.rerun()
            
        # Display info message about current selection
        all_products_selected = all_products_option  # Get directly from the widget value
        if all_products_selected:
            st.info(f'The journey will be saved for the current product ({current_product_name}) and all other products.')
        else:
            # Get selected_products directly from the previous multiselect widget
            if selected_products:
                if len(selected_products) == 1:
                    st.info(f'The journey will be saved for the current product ({current_product_name}) and {selected_products[0]}.')
                else:
                    st.info(f'The journey will be saved for the current product ({current_product_name}) and {len(selected_products)} other selected products.')
            else:
                st.info(f'The journey will be saved for the current product ({current_product_name}) only.')
        
        st.write('---')

        # Confirm Journey button
        if st.button("Confirm Journey"):
            # Get updated journey from the edited DataFrame
            updated_journey = []
            for i, row in edited_df.iterrows():
                stage_dict = dict(row)
                updated_journey.append(stage_dict)
            
            # Add the cross-sell flag and cool-off period to the journey metadata
            if cross_sell_available:
                # Ensure we have the Cool Off Period stage with the current cool_off_period value
                cool_off_stage = None
                for stage in updated_journey:
                    if stage.get('current_stage') == 'Cool Off Period':
                        # Update existing Cool Off Period stage
                        stage['days_to_wait'] = st.session_state.get('cool_off_period', 7)
                        stage['is_cross_sell_stage'] = True
                        cool_off_stage = stage
                        break
                        
                # If Cool Off Period stage doesn't exist in the updated journey, add it
                if cool_off_stage is None:
                    # Generate the next s_no by finding the highest existing s_no and adding 1
                    max_sno = 0
                    for stage in updated_journey:
                        if isinstance(stage.get('s_no'), (int, float)) and stage.get('s_no') > max_sno:
                            max_sno = stage.get('s_no')
                    
                    # Get the cool off period value
                    cool_off_period_value = st.session_state.get('cool_off_period', 7)
                    
                    cool_off_stage = {
                        's_no': max_sno + 1,  # Set s_no to be one higher than the max existing s_no
                        'current_stage': 'Cool Off Period',
                        'description': f'Wait {cool_off_period_value} days before cross-selling to another product',
                        'days_to_wait': cool_off_period_value,
                        'is_cross_sell_stage': True
                    }
                    updated_journey.append(cool_off_stage)
                    
                    # Set the Goal Stage of the last regular stage to Cool Off Period
                    regular_stages = [stage for stage in updated_journey if not stage.get('is_cross_sell_stage', False)]
                    if regular_stages:  # Make sure there's at least one regular stage
                        # Find the last regular stage based on s_no
                        last_regular_stage = max(regular_stages, key=lambda x: x.get('s_no', 0) if isinstance(x.get('s_no'), (int, float)) else 0)
                        # Update its goal_stage
                        last_regular_stage['goal_stage'] = 'Cool Off Period'
            else:
                # Remove any cross-sell stages if cross_sell_available is False
                updated_journey = [stage for stage in updated_journey 
                                  if not stage.get('is_cross_sell_stage', False)]
            
            # Update session state
            st.session_state.user_journey = updated_journey
            
            # Save journey for current product
            if isinstance(st.session_state.product_data, list):
                # If it's a list, use the first item
                current_product_name = st.session_state.product_data[0].get("Product_Name", "") if st.session_state.product_data else ""
            else:
                # If it's a dictionary, use get method
                current_product_name = st.session_state.product_data.get("Product_Name", "")

            # Save journey for current product
            save_user_journey(updated_journey, current_product_name)

            # Get apply_to_all value directly from the widget's key in session state
            apply_to_all = st.session_state.get('apply_journey_to_all', True)
            
            if apply_to_all:
                # Apply to all products except current one
                all_products = get_all_products(filter_by_org=False)
                products_updated = 0
                
                for product in all_products:
                    product_name = product.get("Product_Name", "")
                    # Skip current product as it's already saved
                    if product_name and product_name != current_product_name:
                        save_user_journey(updated_journey, product_name)
                        products_updated += 1
                        
                if products_updated > 0:
                    st.success(f"User journey saved for the current product and {products_updated} other products! Redirecting to Product Priority...")
                else:
                    st.success("User journey saved for the current product! Redirecting to Product Priority...")
            else:
                # Get selected products directly from the widget's key in session state
                selected_products = st.session_state.get('selected_journey_products', [])
                products_updated = 0
                
                for product_name in selected_products:
                    if product_name and product_name != current_product_name:
                        save_user_journey(updated_journey, product_name)
                        products_updated += 1
                        
                if products_updated > 0:
                    if products_updated == 1:
                        st.success(f"User journey saved for the current product and {selected_products[0]}! Redirecting to Product Priority...")
                    else:
                        st.success(f"User journey saved for the current product and {products_updated} other selected products! Redirecting to Product Priority...")
                else:
                    st.success("User journey saved for the current product! Redirecting to Product Priority...")

            st.session_state.show_user_journey = False

            # Set redirection to Product Priority tab
            import time
            time.sleep(2)  # Brief delay to show the success message

            # Reset current view state and set product priority view
            if hasattr(st.session_state, 'reset_other_views'):
                st.session_state.show_product_priority = True
                st.session_state.reset_other_views('show_product_priority')
                st.rerun()

            # Read all stages from user journey
            stages = [stage['current_stage'] for stage in st.session_state.user_journey]

            # Add a message to the chat asking for stage selection
            st.session_state.messages.append({
                "role": "assistant",
                "content": "For which user stage would you like to see the sample communications?"
            })

            # Store stages in session state for selection
            st.session_state.available_stages = stages
            st.rerun()
