"""
Analytics Story UI component for OpenEngage.
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import datetime
from datetime import timedelta
from analytics import (
    process_engagement_data,
    create_user_journey_funnel_chart
)
from core.mail_performance_analyzer import MailPerformanceAnalyzer
from ui.components.chatbot import render_chatbot



def display_analytics_story():
    """Display the analytics story dashboard."""
    # Initialize all session state variables at the top of the function

    # Filter variables
    if 'analytics_story_offering' not in st.session_state:
        st.session_state.analytics_story_offering = "All Offerings"

    if 'analytics_story_audience' not in st.session_state:
        st.session_state.analytics_story_audience = "All Users"

    if 'analytics_story_metric' not in st.session_state:
        st.session_state.analytics_story_metric = 'Total Sent'

    # Date filter variables - will be properly initialized later if needed
    if 'analytics_story_start_date' not in st.session_state:
        st.session_state.analytics_story_start_date = datetime.date.today() - datetime.timedelta(days=30)

    if 'analytics_story_end_date' not in st.session_state:
        st.session_state.analytics_story_end_date = datetime.date.today()

    # Create a header
    st.write("### Analytics Story Dashboard")

    # Channel selector at the top-left
    col1, col2 = st.columns([1, 3])
    with col1:
        channel_options = ["Email", "WhatsApp"]
        selected_channel = st.selectbox(
            "Select Channel",
            options=channel_options,
            key="channel_selector",
            index=0  # Default to Email
        )

    # Show "Coming Soon" message if WhatsApp is selected
    if selected_channel == "WhatsApp":
        st.info("📱 WhatsApp Analytics Coming Soon!")
        return

    # Introduction and overview section for Email
    st.markdown("""
    #### 📊 Analytics Story

    This dashboard provides a comprehensive view of your marketing performance across different channels,
    offerings, and audience segments.

    Click 'Analyze' to process campaign data from the data/campaign_results folder or upload a CSV file.
    """)

    # Add a visual divider
    st.markdown("<hr style='margin: 15px 0; height: 1px; border: none; background-color: #f0f0f5;'>", unsafe_allow_html=True)

    # Check if CSV upload should be hidden
    hide_csv_upload = st.session_state.feature_toggles.get('hide_csv_upload_analytics_story', False) if hasattr(st.session_state, 'feature_toggles') else False

    if hide_csv_upload:
        # Show analyze and update templates buttons when CSV upload is hidden
        analyze_col1, analyze_col2 = st.columns(2)

        with analyze_col1:
            # Analyze button for combined performance file
            year_month = datetime.datetime.now().strftime("%Y%m")
            combined_file_path = f"data/mail_performance/mail_performance_{year_month}.csv"
            analyze_combined_button = st.button("📈 Analyze Data", help=f"Analyze campaign data and combined performance data", type="primary")
            
        with analyze_col2:
            # Button for updating template performance and pruning templates
            update_templates_button = st.button("🔄 Update Templates", help=f"Update template performance metrics and apply pruning logic", type="secondary")

        # Set uploaded_file to None since upload is hidden
        uploaded_file = None
    else:
        # Create columns for the file uploader, analyze button, and update templates button
        upload_col, analyze_col, update_col = st.columns([3, 1, 1])

        with upload_col:
            # File uploader
            uploaded_file = st.file_uploader("Upload CSV file", type=['csv'], key="analytics_story_csv")

        with analyze_col:
            # Analyze button for combined performance file
            year_month = datetime.datetime.now().strftime("%Y%m")
            combined_file_path = f"data/mail_performance/mail_performance_{year_month}.csv"
            analyze_combined_button = st.button("📈 Analyze Data", help=f"Analyze campaign data and combined performance data", type="primary")
            
        with update_col:
            # Button for updating template performance and pruning templates
            update_templates_button = st.button("🔄 Update Templates", help=f"Update template performance metrics and apply pruning logic", type="secondary")

    # Hidden variable to track if analyze button was clicked (for backward compatibility)
    analyze_button = False

    # Process data either from uploaded file or from analyze buttons
    if uploaded_file is not None or analyze_button or analyze_combined_button or update_templates_button:
        # Variable to track if we should continue with dashboard display
        show_dashboard = False

        # Initialize df as None at the beginning to ensure it exists in all code paths
        df = None
        
        try:
            # Handle the Update Templates button click - only update template performance without reanalyzing campaigns
            if update_templates_button:
                with st.spinner("Updating template performance metrics and applying pruning logic..."):
                    # Use the dedicated function from analytics.py
                    from openengage.analytics import update_template_performance, create_template_performance_chart
                    
                    # Debug statement before calling update_template_performance
                    print("DEBUG UI: Before calling update_template_performance")
                    # Update template performance and get the results (now returns both df and thresholds)
                    result = update_template_performance()
                    print(f"DEBUG UI: update_template_performance returned result type: {type(result)}")
                    
                    # Safely unpack results
                    if isinstance(result, tuple) and len(result) == 2:
                        template_df, threshold_values = result
                        print(f"DEBUG UI: Successfully unpacked tuple with df shape: {template_df.shape if hasattr(template_df, 'shape') else 'unknown'}")
                    else:
                        print(f"DEBUG UI: Unexpected return format: {result}")
                        # Handle incorrect return format
                        template_df = result if isinstance(result, pd.DataFrame) else pd.DataFrame()
                        threshold_values = {}
                    
                    if not template_df.empty:
                        # Store thresholds in session state
                        st.session_state.template_thresholds = threshold_values
                        # Store in session state for later visualization
                        st.session_state.template_performance_df = template_df
                        
                        # Also store the raw performance data if available
                        import glob
                        import os
                        combined_files = glob.glob('data/mail_performance/combined/all_performance_*.csv')
                        if combined_files:
                            latest_file = max(combined_files, key=os.path.getmtime)
                            if os.path.exists(latest_file):
                                df = pd.read_csv(latest_file)
                        
                        # Extract some stats for display
                        total_templates = len(template_df)
                        pruned_templates = template_df['Pruning_Flag'].sum()
                        stages = template_df['Stage'].nunique()
                        
                        # Success message with stats
                        st.success("Template performance updated successfully")
                        st.info(f"📊 Templates processed: {total_templates} across {stages} stages")
                        st.info(f"📉 Templates flagged for pruning: {pruned_templates} ({round(pruned_templates/total_templates*100, 1)}%)")
                        
                        # Set flag to show dashboard with updated metrics
                        show_dashboard = True
                    else:
                        st.warning("No template performance data could be generated. Make sure you have run 'Analyze Data' first.")
                        return
                
            # If analyze combined button was clicked, first run the analyze campaigns functionality
            # and then load the combined performance file
            elif analyze_combined_button:
                # First, run the analyze campaigns functionality
                with st.spinner("Step 1/2: Analyzing campaign data from data/campaign_results folder..."):
                    # Create an instance of the MailPerformanceAnalyzer
                    analyzer = MailPerformanceAnalyzer()

                    # Analyze all campaigns
                    performance_data = analyzer.analyze_all_campaigns()

                    if performance_data.empty:
                        st.warning("No campaign data found. Please make sure there are CSV files in the data/campaign_results folder.")
                        return
                    else:
                        # Get performance summary
                        summary = analyzer.get_performance_summary()

                        # Store the summary in session state for later use
                        st.session_state.campaign_summary = summary
                        st.session_state.campaign_analyzed = True
                        year_month = datetime.datetime.now().strftime("%Y%m")

                        # Store the performance data path in session state
                        from pathlib import Path  # Import Path here to ensure it's available
                        performance_file = Path(f"data/mail_performance/combined/all_performance_{year_month}.csv")
                        if performance_file.exists():
                            st.session_state.performance_file_path = str(performance_file)

                        # Use the performance data as our dataframe
                        df = performance_data
                        st.success(f"Step 1 complete: Successfully analyzed campaign data with {len(df)} records.")

                # Then, load the combined performance file
                with st.spinner(f"Step 2/2: Analyzing combined performance data from {combined_file_path}..."):
                    import os

                    # Check if the file exists
                    if os.path.exists(combined_file_path):
                        # Read the combined performance file
                        df = pd.read_csv(combined_file_path)
                        
                        # Run the template performance analysis
                        from openengage.analytics import analyze_template_performance, create_template_performance_chart
                        
                        # Analyze template performance and save to CSV
                        with st.spinner("Analyzing template performance..."):
                            print("DEBUG UI: Before calling analyze_template_performance in Analyze Data")
                            result = analyze_template_performance(df)
                            print(f"DEBUG UI: analyze_template_performance returned result type: {type(result)}")
                            
                            # Safely unpack results
                            if isinstance(result, tuple) and len(result) == 2:
                                template_df, threshold_values = result
                                print(f"DEBUG UI: Analyze Data - Successfully unpacked tuple with df shape: {template_df.shape if hasattr(template_df, 'shape') else 'unknown'}")
                            else:
                                print(f"DEBUG UI: Analyze Data - Unexpected return format: {result}")
                                # Handle incorrect return format
                                template_df = result if isinstance(result, pd.DataFrame) else pd.DataFrame()
                                threshold_values = {}
                                
                            if not template_df.empty:
                                st.success(f"Template performance data analyzed and saved with {len(template_df)} templates")
                                # Print actual values from analytics.py before assigning to session state
                                print(f"RAW DEBUG - Threshold values received from analytics.py: {threshold_values}")
                                if threshold_values:
                                    for stage, values in threshold_values.items():
                                        print(f"RAW DEBUG - Stage {stage}: CTO = {values.get('click_to_open_rate')}, Open = {values.get('open_rate')}")

                                # Store in session state for later visualization
                                st.session_state.template_performance_df = template_df
                                st.session_state.template_thresholds = threshold_values
                        
                        # Ensure all necessary columns are properly formatted
                        if 'Send_Time' in df.columns:
                            df['Send_Time'] = pd.to_datetime(df['Send_Time'], errors='coerce')

                        if 'Open_Time' in df.columns:
                            df['Open_Time'] = pd.to_datetime(df['Open_Time'], errors='coerce')

                        if 'Click_Time' in df.columns:
                            df['Click_Time'] = pd.to_datetime(df['Click_Time'], errors='coerce')

                        # Create a simple summary for session state
                        total_emails = len(df)
                        open_count = df['Open_Time'].notna().sum() if 'Open_Time' in df.columns else 0
                        click_count = df['Click_Time'].notna().sum() if 'Click_Time' in df.columns else 0

                        open_rate = (open_count / total_emails) * 100 if total_emails > 0 else 0
                        click_rate = (click_count / total_emails) * 100 if total_emails > 0 else 0
                        click_to_open_rate = (click_count / open_count) * 100 if open_count > 0 else 0

                        # Create visualization data
                        if 'Send_Time' in df.columns:
                            # Extract date part
                            df['date'] = df['Send_Time'].dt.date

                            # Group by date
                            viz_data = df.groupby('date').agg({
                                'user_email': 'count',
                                'Open_Time': lambda x: x.notna().sum(),
                                'Click_Time': lambda x: x.notna().sum()
                            }).reset_index()

                            # Rename columns
                            viz_data = viz_data.rename(columns={
                                'user_email': 'total_sent',
                                'Open_Time': 'total_opened',
                                'Click_Time': 'total_clicked'
                            })

                            # Calculate rates
                            viz_data['open_rate'] = (viz_data['total_opened'] / viz_data['total_sent']) * 100
                            viz_data['click_rate'] = (viz_data['total_clicked'] / viz_data['total_sent']) * 100

                            # Convert date to string for serialization
                            viz_data['date'] = viz_data['date'].astype(str)

                            # Convert to dict for storing
                            visualization_data = viz_data.to_dict('records')
                        else:
                            visualization_data = None

                        # Pre-calculate template, subject, and personalized mail performance metrics for later use
                        template_performance = None
                        subject_performance = None
                        personalized_mail_performance = None

                        # Calculate subject performance if possible
                        if 'Subject' in df.columns:
                            # Create binary columns for opens and sends
                            df['Open'] = df['Open_Time'].apply(lambda x: 0 if pd.isna(x) else 1)

                            # For sends, use Campaign_ID if available, otherwise fall back to other columns
                            if 'Campaign_ID' in df.columns:
                                df['Sent'] = df['Campaign_ID'].apply(lambda x: 0 if pd.isna(x) else 1)
                            elif 'ISO_Date_Time' in df.columns:
                                df['Sent'] = df['ISO_Date_Time'].apply(lambda x: 0 if pd.isna(x) else 1)
                            elif 'Send_Time' in df.columns:
                                df['Sent'] = df['Send_Time'].apply(lambda x: 0 if pd.isna(x) else 1)
                            else:
                                # If no suitable column is available, assume all rows were sent
                                df['Sent'] = 1

                            # Group by subject and sum the opens and sends
                            subject_performance = df.groupby('Subject')[['Open', 'Sent']].sum().reset_index()

                            # Calculate open rate (opens / sends)
                            subject_performance['Open Rate'] = (subject_performance['Open'] / subject_performance['Sent']) * 100
                            subject_performance['Open Rate'] = subject_performance['Open Rate'].fillna(0)

                            # Rename columns for consistency with the rest of the code
                            subject_performance = subject_performance.rename(columns={
                                'Open': 'Open_Time',
                                'Sent': 'user_email'
                            })

                            # Add product information if available
                            if 'product' in df.columns:
                                product_mapping = df.groupby('Subject')['product'].first().reset_index()
                                subject_performance = subject_performance.merge(product_mapping, on='Subject', how='left')
                            elif 'offering' in df.columns:
                                product_mapping = df.groupby('Subject')['offering'].first().reset_index()
                                subject_performance = subject_performance.merge(product_mapping, on='Subject', how='left')
                                subject_performance.rename(columns={'offering': 'product'}, inplace=True)
                            elif 'Matched_Product' in df.columns:
                                product_mapping = df.groupby('Subject')['Matched_Product'].first().reset_index()
                                subject_performance = subject_performance.merge(product_mapping, on='Subject', how='left')
                                subject_performance.rename(columns={'Matched_Product': 'product'}, inplace=True)

                        # Calculate template performance if possible
                        template_column = None
                        if 'template' in df.columns:
                            template_column = 'template'
                        elif 'base_mails' in df.columns:
                            template_column = 'base_mails'
                        elif 'email-template' in df.columns:
                            template_column = 'email-template'
                        elif 'template-id' in df.columns:
                            template_column = 'template-id'

                        if template_column and 'Open_Time' in df.columns and 'Click_Time' in df.columns:
                            template_performance = df.groupby(template_column).agg({
                                'user_email': 'count',
                                'Open_Time': lambda x: x.notna().sum(),
                                'Click_Time': lambda x: x.notna().sum()
                            }).reset_index()

                            # Calculate pure click rate (clicks/opens)
                            template_performance['Pure Click Rate'] = (template_performance['Click_Time'] / template_performance['Open_Time']) * 100
                            template_performance['Pure Click Rate'] = template_performance['Pure Click Rate'].fillna(0)
                            template_performance.loc[template_performance['Pure Click Rate'] == float('inf'), 'Pure Click Rate'] = 0

                            # Calculate open rate for reference
                            template_performance['Open Rate'] = (template_performance['Open_Time'] / template_performance['user_email']) * 100
                            template_performance['Open Rate'] = template_performance['Open Rate'].fillna(0)

                            # Add sample subject for each template if possible
                            if 'Subject' in df.columns:
                                sample_subjects = df.groupby(template_column)['Subject'].first().reset_index()
                                template_performance = template_performance.merge(sample_subjects, on=template_column, how='left')

                            # Add product information if available
                            if 'product' in df.columns:
                                product_mapping = df.groupby(template_column)['product'].first().reset_index()
                                template_performance = template_performance.merge(product_mapping, on=template_column, how='left')
                            elif 'offering' in df.columns:
                                product_mapping = df.groupby(template_column)['offering'].first().reset_index()
                                template_performance = template_performance.merge(product_mapping, on=template_column, how='left')
                                template_performance.rename(columns={'offering': 'product'}, inplace=True)
                            elif 'Matched_Product' in df.columns:
                                product_mapping = df.groupby(template_column)['Matched_Product'].first().reset_index()
                                template_performance = template_performance.merge(product_mapping, on=template_column, how='left')
                                template_performance.rename(columns={'Matched_Product': 'product'}, inplace=True)

                        # Calculate personalized mail performance if possible
                        if 'personalized_mails' in df.columns and 'Open_Time' in df.columns and 'Click_Time' in df.columns:
                            personalized_mail_performance = df.groupby('personalized_mails').agg({
                                'user_email': 'count',
                                'Open_Time': lambda x: x.notna().sum(),
                                'Click_Time': lambda x: x.notna().sum()
                            }).reset_index()

                            # Calculate pure click rate (clicks/opens)
                            personalized_mail_performance['Pure Click Rate'] = (personalized_mail_performance['Click_Time'] / personalized_mail_performance['Open_Time']) * 100
                            personalized_mail_performance['Pure Click Rate'] = personalized_mail_performance['Pure Click Rate'].fillna(0)
                            personalized_mail_performance.loc[personalized_mail_performance['Pure Click Rate'] == float('inf'), 'Pure Click Rate'] = 0

                            # Calculate open rate for reference
                            personalized_mail_performance['Open Rate'] = (personalized_mail_performance['Open_Time'] / personalized_mail_performance['user_email']) * 100
                            personalized_mail_performance['Open Rate'] = personalized_mail_performance['Open Rate'].fillna(0)

                            # Add sample subject for each personalized mail if possible
                            if 'Subject' in df.columns:
                                sample_subjects = df.groupby('personalized_mails')['Subject'].first().reset_index()
                                personalized_mail_performance = personalized_mail_performance.merge(sample_subjects, on='personalized_mails', how='left')

                            # Add product information if available
                            if 'product' in df.columns:
                                product_mapping = df.groupby('personalized_mails')['product'].first().reset_index()
                                personalized_mail_performance = personalized_mail_performance.merge(product_mapping, on='personalized_mails', how='left')
                            elif 'offering' in df.columns:
                                product_mapping = df.groupby('personalized_mails')['offering'].first().reset_index()
                                personalized_mail_performance = personalized_mail_performance.merge(product_mapping, on='personalized_mails', how='left')
                                personalized_mail_performance.rename(columns={'offering': 'product'}, inplace=True)
                            elif 'Matched_Product' in df.columns:
                                product_mapping = df.groupby('personalized_mails')['Matched_Product'].first().reset_index()
                                personalized_mail_performance = personalized_mail_performance.merge(product_mapping, on='personalized_mails', how='left')
                                personalized_mail_performance.rename(columns={'Matched_Product': 'product'}, inplace=True)

                        # Store summary in session state
                        st.session_state.campaign_summary = {
                            "total_emails": total_emails,
                            "open_count": open_count,
                            "open_rate": open_rate,
                            "click_count": click_count,
                            "click_rate": click_rate,
                            "click_to_open_rate": click_to_open_rate,
                            "visualization_data": visualization_data,
                            "template_performance": template_performance.to_dict('records') if template_performance is not None else None,
                            "subject_performance": subject_performance.to_dict('records') if subject_performance is not None else None,
                            "personalized_mail_performance": personalized_mail_performance.to_dict('records') if personalized_mail_performance is not None else None
                        }
                        st.session_state.campaign_analyzed = True

                        # Store the performance file path in session state
                        st.session_state.performance_file_path = combined_file_path

                        st.success(f"Step 2 complete: Successfully loaded combined performance data with {len(df)} records.")
                    else:
                        st.warning(f"Combined performance file not found at {combined_file_path}. Using campaign data from step 1 instead.")
                        # We already have the data from step 1, so we can continue
            # This section is kept for backward compatibility but should never be executed
            # since we've hidden the analyze_button
            elif analyze_button:
                st.warning("This code path should not be executed. Please use the 'Analyze Data' button instead.")
                return
            else:
                # Read uploaded CSV
                df = pd.read_csv(uploaded_file)
                st.success(f"Successfully processed uploaded file with {len(df)} records.")

            # Process the data if it's not already processed
            if analyze_button and hasattr(st.session_state, 'campaign_analyzed') and st.session_state.campaign_analyzed:
                # Data is already processed by the MailPerformanceAnalyzer
                processed_df = df
            else:
                # Process the data using the analytics module
                processed_df = process_engagement_data(df)

            # Store in session state
            st.session_state.analytics_story_data = processed_df

            # Set flag to show dashboard
            show_dashboard = True

            # Add a visual divider
            st.markdown("<hr style='margin: 15px 0; height: 1px; border: none; background-color: #f0f0f5;'>", unsafe_allow_html=True)
        except Exception as e:
            st.error(f"Error processing data: {str(e)}")
            return

        # Only continue if data was successfully processed
        if show_dashboard:
            # Create tabs for Analytics Story and Insights
            analytics_story_tab, insights_tab = st.tabs(["Analytics Story", "Insights"])

            # Analytics Story Tab
            with analytics_story_tab:
                # User Base Health Section - make it collapsible but open by default
                with st.expander("### 👥 User Base Health", expanded=True):
                    # Calculate user metrics from the data
                    # Assuming the CSV has a 'user_email' column and possibly a 'user_status' column
                    if 'user_email' in df.columns:
                        # Count unique users
                        total_users = df['user_email'].nunique()

                        # Determine active/inactive users based on Last_Open_Time
                        # If not opened in last 3 months and user activity is empty, consider inactive
                        # Convert Last_Open_Time to datetime if it exists
                        if 'Last_Open_Time' in df.columns:
                            # Convert to datetime and make timezone-naive to avoid comparison issues
                            df['Last_Open_Time'] = pd.to_datetime(df['Last_Open_Time'], errors='coerce')
                            
                            # Force all timezone-aware datetimes to naive by converting to timestamp then back to datetime
                            # This completely removes timezone info for consistent comparison
                            print("DEBUG: Converting datetimes to timezone-naive for comparison")
                            
                            # First, identify any non-NaT values to avoid errors
                            valid_mask = ~df['Last_Open_Time'].isna()
                            
                            if valid_mask.any():
                                # Convert timezone-aware to naive by removing the timezone info completely
                                try:
                                    df.loc[valid_mask, 'Last_Open_Time'] = df.loc[valid_mask, 'Last_Open_Time'].dt.tz_localize(None)
                                    print(f"DEBUG: Last_Open_Time dtype after conversion: {df['Last_Open_Time'].dtype}")
                                except Exception as e:
                                    # If already localized, convert to UTC then remove timezone
                                    print(f"DEBUG: Timezone conversion error: {e}")
                                    try:
                                        df.loc[valid_mask, 'Last_Open_Time'] = df.loc[valid_mask, 'Last_Open_Time'].dt.tz_convert('UTC').dt.tz_localize(None)
                                    except Exception as e2:
                                        print(f"DEBUG: Second timezone conversion failed: {e2}")
                                        
                            # Create three_months_ago as timezone-naive
                            current_date = datetime.datetime.now()
                            # Remove timezone info to match the converted column
                            current_date = current_date.replace(tzinfo=None)
                            three_months_ago = current_date - timedelta(days=90)
                            print(f"DEBUG: three_months_ago: {three_months_ago}, type: {type(three_months_ago)}")
                            print(f"DEBUG: three_months_ago timezone info: {three_months_ago.tzinfo}")
                            
                            if valid_mask.any():
                                print(f"DEBUG: Sample Last_Open_Time: {df.loc[valid_mask, 'Last_Open_Time'].iloc[0]}, type: {type(df.loc[valid_mask, 'Last_Open_Time'].iloc[0])}")
                                if hasattr(df.loc[valid_mask, 'Last_Open_Time'].iloc[0], 'tzinfo'):
                                    print(f"DEBUG: Sample Last_Open_Time timezone: {df.loc[valid_mask, 'Last_Open_Time'].iloc[0].tzinfo}")
                                else:
                                    print("DEBUG: Last_Open_Time has no tzinfo attribute")
                            else:
                                print("DEBUG: No valid Last_Open_Time dates to check")
                                    

                            # Check if user_behaviour exists
                            if 'user_behaviour' in df.columns:
                                # Inactive: Not opened in last 3 months AND empty user_behaviour
                                # Create a safe comparison function that handles timezone differences
                                def safe_datetime_compare(date_series, threshold):
                                    if date_series.empty:
                                        return pd.Series([False] * len(date_series))
                                    
                                    # Create a result series with same index as input
                                    result = pd.Series([False] * len(date_series), index=date_series.index)
                                    
                                    # For each value, compare safely (handling both NaT and timezone issues)
                                    for idx in date_series.index:
                                        val = date_series[idx]
                                        if pd.isna(val):
                                            result[idx] = True  # NaT values count as "less than threshold"
                                        else:
                                            # Convert to timestamp (numeric) for comparison
                                            try:
                                                # Try to get timestamp (this works for both tz-aware and naive)
                                                val_ts = pd.Timestamp(val).timestamp()
                                                threshold_ts = pd.Timestamp(threshold).timestamp()
                                                result[idx] = val_ts < threshold_ts
                                            except Exception as e:
                                                print(f"DEBUG: Error in datetime comparison: {e} - treating as NaT")
                                                result[idx] = True
                                    
                                    return result
                                
                                # Use our safe comparison function
                                time_condition = safe_datetime_compare(df['Last_Open_Time'], three_months_ago)
                                user_behavior_condition = (df['user_behaviour'].isna()) | (df['user_behaviour'] == '')
                                inactive_condition = time_condition & user_behavior_condition
                            else:
                                # If no user_behaviour column, just use Last_Open_Time with safe comparison
                                # Reuse the safe_datetime_compare function from above
                                time_condition = safe_datetime_compare(df['Last_Open_Time'], three_months_ago)
                                inactive_condition = time_condition

                            inactive_users = df[inactive_condition]['user_email'].nunique()
                            active_users = total_users - inactive_users
                        elif 'engagement_status' in df.columns:
                            # If we have engagement_status column, use it
                            active_users = df[df['engagement_status'] == 'active']['user_email'].nunique()
                            inactive_users = df[df['engagement_status'] == 'inactive']['user_email'].nunique()
                        else:
                            # Fallback to Open_Time if Last_Open_Time doesn't exist
                            if 'Open_Time' in df.columns:
                                df['Open_Time'] = pd.to_datetime(df['Open_Time'], errors='coerce')
                                inactive_condition = (df['Open_Time'].isna()) | (df['Open_Time'] < three_months_ago)
                                inactive_users = df[inactive_condition]['user_email'].nunique()
                                active_users = total_users - inactive_users
                            else:
                                # If no open time data at all, assume all users are inactive
                                active_users = 0
                                inactive_users = total_users

                        # Calculate percentages
                        active_percentage = (active_users / total_users) * 100 if total_users > 0 else 0
                        inactive_percentage = (inactive_users / total_users) * 100 if total_users > 0 else 0

                        # Display user metrics in columns
                        col1, col2, col3 = st.columns(3)

                        # Userbase metrics
                        with col1:
                            st.metric("Total Userbase", f"{total_users:,}")
                            st.markdown("<div style='font-size: 0.8em; color: #666;'>Total unique users</div>", unsafe_allow_html=True)

                        # Active users
                        with col2:
                            st.metric("Active Users", f"{active_users:,}", delta=f"{active_percentage:.1f}% of total")
                            st.markdown("<div style='font-size: 0.8em; color: #666;'>Users who have engaged</div>", unsafe_allow_html=True)

                        # Inactive users
                        with col3:
                            st.metric("Inactive Users", f"{inactive_users:,}", delta=f"{inactive_percentage:.1f}% of total", delta_color="inverse")
                            st.markdown("<div style='font-size: 0.8em; color: #666;'>Users who haven't engaged</div>", unsafe_allow_html=True)
                    else:
                        st.warning("User metrics cannot be calculated. The CSV file must contain a 'user_email' column.")

                # Add a visual divider
                st.markdown("<hr style='margin: 15px 0; height: 1px; border: none; background-color: #f0f0f5;'>", unsafe_allow_html=True)

                # Filters Section
                st.markdown("### 🔍 Filters")

                # Create filter columns
                filter_col1, filter_col2 = st.columns(2)

                with filter_col1:
                    # Get offering options from the data if available
                    offering_options = ["All Offerings"]
                    if 'offering' in df.columns:
                        offering_options.extend(df['offering'].dropna().unique())
                    elif 'product' in df.columns:
                        offering_options.extend(df['product'].dropna().unique())
                    elif 'Matched_Product' in df.columns:
                        offering_options.extend(df['Matched_Product'].dropna().unique())
                    elif 'last_product_sent' in df.columns:
                        offering_options.extend(df['last_product_sent'].dropna().unique())
                    elif 'Last_Product_Sent' in df.columns:
                        offering_options.extend(df['Last_Product_Sent'].dropna().unique())

                    # Ensure stored offering is in the available options
                    stored_offering = st.session_state.analytics_story_offering
                    if stored_offering not in offering_options:
                        stored_offering = "All Offerings"

                    offering = st.selectbox(
                        "Offering",
                        options=offering_options,
                        key="analytics_story_offering",
                        index=offering_options.index(stored_offering)
                    )

                with filter_col2:
                    # Define audience options
                    audience_options = ["All Users", "Active Users", "Inactive Users"]

                    audience_type = st.selectbox(
                        "Audience Type",
                        options=audience_options,
                        key="analytics_story_audience",
                        index=audience_options.index(st.session_state.analytics_story_audience)
                    )

                # Add a visual divider
                st.markdown("<hr style='margin: 15px 0; height: 1px; border: none; background-color: #f0f0f5;'>", unsafe_allow_html=True)

                # Apply filters to the data
                filtered_df = processed_df.copy()

                # Set channel to Email since we're only showing Email data now
                channel = "Email"

                # Filter by offering if selected
                if offering != "All Offerings":
                    if 'offering' in df.columns:
                        filtered_df = filtered_df[df['offering'] == offering]
                    elif 'product' in df.columns:
                        filtered_df = filtered_df[df['product'] == offering]
                    elif 'Matched_Product' in df.columns:
                        filtered_df = filtered_df[df['Matched_Product'] == offering]
                    elif 'last_product_sent' in df.columns:
                        filtered_df = filtered_df[df['last_product_sent'] == offering]
                    elif 'Last_Product_Sent' in df.columns:
                        filtered_df = filtered_df[df['Last_Product_Sent'] == offering]

                # Filter by audience type using the same logic as for user metrics
                if audience_type != "All Users":
                    # Determine inactive condition based on Last_Open_Time
                    # Convert to timezone-naive datetime for consistent comparison
                    
                    if 'Last_Open_Time' in df.columns:
                        # Convert Last_Open_Time to datetime if it's not already
                        if not pd.api.types.is_datetime64_any_dtype(df['Last_Open_Time']):
                            df['Last_Open_Time'] = pd.to_datetime(df['Last_Open_Time'], errors='coerce')
                        
                        # Force all timezone-aware datetimes to naive by converting to timestamp then back to datetime
                        # First, identify any non-NaT values to avoid errors
                        valid_mask = ~df['Last_Open_Time'].isna()
                        
                        if valid_mask.any():
                            # Convert timezone-aware to naive by removing the timezone info completely
                            try:
                                df.loc[valid_mask, 'Last_Open_Time'] = df.loc[valid_mask, 'Last_Open_Time'].dt.tz_localize(None)
                            except Exception as e:
                                print(f"DEBUG (audience filter): Timezone conversion error: {e}")
                                try:
                                    # If already localized, convert to UTC then remove timezone
                                    df.loc[valid_mask, 'Last_Open_Time'] = df.loc[valid_mask, 'Last_Open_Time'].dt.tz_convert('UTC').dt.tz_localize(None)
                                except Exception as e2:
                                    print(f"DEBUG (audience filter): Second timezone conversion failed: {e2}")
                        
                        # Create naive datetime for comparison
                        current_date = datetime.datetime.now().replace(tzinfo=None)
                        three_months_ago = current_date - timedelta(days=90)

                        # Check if user_behaviour exists
                        # Define a safe datetime comparison function
                        def safe_datetime_compare(date_series, threshold):
                            if date_series.empty:
                                return pd.Series([False] * len(date_series))
                            
                            # Create a result series with same index as input
                            result = pd.Series([False] * len(date_series), index=date_series.index)
                            
                            # For each value, compare safely (handling both NaT and timezone issues)
                            for idx in date_series.index:
                                val = date_series[idx]
                                if pd.isna(val):
                                    result[idx] = True  # NaT values count as "less than threshold"
                                else:
                                    # Convert to timestamp (numeric) for comparison
                                    try:
                                        # Try to get timestamp (this works for both tz-aware and naive)
                                        val_ts = pd.Timestamp(val).timestamp()
                                        threshold_ts = pd.Timestamp(threshold).timestamp()
                                        result[idx] = val_ts < threshold_ts
                                    except Exception as e:
                                        print(f"DEBUG (audience): Error in datetime comparison: {e} - treating as NaT")
                                        result[idx] = True
                            
                            return result
                        
                        if 'user_behaviour' in df.columns:
                            # Inactive: Not opened in last 3 months AND empty user_behaviour
                            # Use the safe comparison function
                            time_condition = safe_datetime_compare(df['Last_Open_Time'], three_months_ago)
                            user_behavior_condition = (df['user_behaviour'].isna()) | (df['user_behaviour'] == '')
                            inactive_condition = time_condition & user_behavior_condition
                        else:
                            # If no user_behaviour column, just use Last_Open_Time with safe comparison
                            time_condition = safe_datetime_compare(df['Last_Open_Time'], three_months_ago)
                            inactive_condition = time_condition

                        if audience_type == "Active Users":
                            filtered_df = filtered_df[~inactive_condition]
                        elif audience_type == "Inactive Users":
                            filtered_df = filtered_df[inactive_condition]
                    elif 'engagement_status' in df.columns:
                        # If we have engagement_status column, use it
                        if audience_type == "Active Users":
                            filtered_df = filtered_df[df['engagement_status'] == 'active']
                        elif audience_type == "Inactive Users":
                            filtered_df = filtered_df[df['engagement_status'] == 'inactive']
                    elif 'Open_Time' in df.columns:
                        # Fallback to Open_Time if Last_Open_Time doesn't exist
                        # Convert Open_Time to datetime if it's not already
                        if not pd.api.types.is_datetime64_any_dtype(df['Open_Time']):
                            df['Open_Time'] = pd.to_datetime(df['Open_Time'], errors='coerce')

                        inactive_condition = (df['Open_Time'].isna()) | (df['Open_Time'] < three_months_ago)

                        if audience_type == "Active Users":
                            filtered_df = filtered_df[~inactive_condition]
                        elif audience_type == "Inactive Users":
                            filtered_df = filtered_df[inactive_condition]

                # Channel Health Section
                st.markdown("### 📈 Channel Health")

                # Store the current filter values in variables to avoid page refresh
                current_metric = st.session_state.analytics_story_metric

                # Create a form for filters to ensure they're processed together
                with st.form(key="channel_health_filters"):
                    metric_col, date_col1, date_col2 = st.columns([1, 1, 1])

                    # Inside the form, create the metric selector
                    with metric_col:
                        # Create metric selector
                        selected_metric = st.selectbox(
                            "Select Metric",
                            options=['Total Sent', 'Total Opens', 'Total Clicks'],
                            key='analytics_story_metric',
                            index=['Total Sent', 'Total Opens', 'Total Clicks'].index(current_metric)
                        )

                    # Add date inputs inside the form
                    if 'date' in filtered_df.columns or 'Send_Time' in filtered_df.columns or 'Last_Send_Time' in filtered_df.columns:
                        # Determine date column to use
                        date_column = None
                        if 'date' in filtered_df.columns:
                            date_column = 'date'
                        elif 'Send_Time' in filtered_df.columns:
                            date_column = 'Send_Time'
                        elif 'Last_Send_Time' in filtered_df.columns:
                            date_column = 'Last_Send_Time'

                    # Get min and max dates for the date range picker
                    if date_column:
                        # Convert to datetime if not already
                        if not pd.api.types.is_datetime64_any_dtype(filtered_df[date_column]):
                            filtered_df[date_column] = pd.to_datetime(filtered_df[date_column], errors='coerce')

                        # Get min and max dates
                        valid_dates = filtered_df[filtered_df[date_column].notna()]
                        if not valid_dates.empty:
                            min_date = valid_dates[date_column].min().date()
                            max_date = valid_dates[date_column].max().date()

                            # Initialize date filters if not already done
                            if 'analytics_story_start_date' not in st.session_state:
                                st.session_state.analytics_story_start_date = min_date
                            if 'analytics_story_end_date' not in st.session_state:
                                st.session_state.analytics_story_end_date = max_date

                            # Ensure stored dates are within valid range
                            stored_start_date = st.session_state.analytics_story_start_date
                            if isinstance(stored_start_date, datetime.datetime):
                                stored_start_date = stored_start_date.date()
                            if stored_start_date < min_date or stored_start_date > max_date:
                                stored_start_date = min_date
                                st.session_state.analytics_story_start_date = min_date

                            stored_end_date = st.session_state.analytics_story_end_date
                            if isinstance(stored_end_date, datetime.datetime):
                                stored_end_date = stored_end_date.date()
                            if stored_end_date < min_date or stored_end_date > max_date:
                                stored_end_date = max_date
                                st.session_state.analytics_story_end_date = max_date

                            # Add date inputs to the form
                            with date_col1:
                                start_date = st.date_input(
                                    "Start Date",
                                    value=stored_start_date,
                                    min_value=min_date,
                                    max_value=max_date,
                                    key='analytics_story_start_date'
                                )

                            with date_col2:
                                end_date = st.date_input(
                                    "End Date",
                                    value=stored_end_date,
                                    min_value=min_date,
                                    max_value=max_date,
                                    key='analytics_story_end_date'
                                )

                    # Add a submit button to apply filters
                    submit_button = st.form_submit_button(label="Apply Filters")

                # If form is submitted, update session state
                if submit_button:
                    pass  # Form submission handled by Streamlit automatically

                # Use the stored values for rendering

                # Check if we have campaign summary data from the analyzer
                has_campaign_summary = hasattr(st.session_state, 'campaign_summary') and st.session_state.campaign_summary

                # Get start and end dates from session state
                start_date = st.session_state.analytics_story_start_date if 'analytics_story_start_date' in st.session_state else None
                end_date = st.session_state.analytics_story_end_date if 'analytics_story_end_date' in st.session_state else None

                # Ensure end_date is not before start_date
                if start_date and end_date and end_date < start_date:
                    st.warning("End date cannot be before start date. Using start date for both.")
                    end_date = start_date
                    st.session_state.analytics_story_end_date = start_date

                # Date validation
                if not (start_date and end_date):
                    st.warning("No valid date information found in the data.")
                    start_date = None
                    end_date = None

                # Check if we have campaign summary data with visualization data
                if has_campaign_summary and 'visualization_data' in st.session_state.campaign_summary:
                    # Use visualization data from campaign summary
                    viz_data = st.session_state.campaign_summary['visualization_data']

                    # Convert to DataFrame
                    grouped_df = pd.DataFrame(viz_data)

                    # Convert date column to datetime
                    grouped_df['date'] = pd.to_datetime(grouped_df['date'])

                    # Filter by date range if available
                    if start_date and end_date:
                        # Convert start_date and end_date to datetime.date if they're datetime objects
                        if isinstance(start_date, datetime.datetime):
                            start_date = start_date.date()
                        if isinstance(end_date, datetime.datetime):
                            end_date = end_date.date()

                        # Convert grouped_df['date'] to date for comparison
                        grouped_df = grouped_df[
                            (grouped_df['date'].dt.date >= start_date) &
                            (grouped_df['date'].dt.date <= end_date)
                        ]

                    # Create a simple line chart for the selected metric
                    fig = go.Figure()

                    # Determine which column to use for each metric based on what's available
                    sent_col = 'total_sent' if 'total_sent' in grouped_df.columns else None
                    open_col = 'total_opened' if 'total_opened' in grouped_df.columns else None
                    click_col = 'total_clicked' if 'total_clicked' in grouped_df.columns else None

                    # Map selected metric to column name
                    metric_column = None
                    if selected_metric == 'Total Sent' and sent_col:
                        metric_column = sent_col
                    elif selected_metric == 'Total Opens' and open_col:
                        metric_column = open_col
                    elif selected_metric == 'Total Clicks' and click_col:
                        metric_column = click_col

                    # Map selected metric to color
                    metric_color = {
                        'Total Sent': '#1f77b4',
                        'Total Opens': '#ff7f0e',
                        'Total Clicks': '#2ca02c'
                    }[selected_metric]

                    # Add trace for selected metric if the column exists
                    if metric_column and metric_column in grouped_df.columns:
                        fig.add_trace(
                            go.Scatter(
                                x=grouped_df['date'],
                                y=grouped_df[metric_column],
                                name=selected_metric,
                                marker_color=metric_color,
                                mode='lines+markers',
                                line=dict(width=3)
                            )
                        )
                    else:
                        # If the metric column doesn't exist, show a message
                        st.warning(f"Data for {selected_metric} is not available in the visualization data.")

                    # Add figure title
                    fig.update_layout(
                        title_text=f"{selected_metric} Over Time",
                        hovermode="x unified",
                        height=500,
                        margin=dict(l=20, r=20, t=50, b=20),
                        xaxis=dict(
                            title=dict(
                                text="Date",
                                font=dict(size=14)
                            ),
                            tickfont=dict(size=12)
                        ),
                        yaxis=dict(
                            title=dict(
                                text=selected_metric,
                                font=dict(size=14)
                            ),
                            tickfont=dict(size=12)
                        )
                    )

                    # Display the chart
                    st.plotly_chart(fig, use_container_width=True)
                # Group data by date from the filtered dataframe
                elif date_column and date_column in filtered_df.columns:
                    # Convert to datetime if not already
                    if not pd.api.types.is_datetime64_any_dtype(filtered_df[date_column]):
                        filtered_df[date_column] = pd.to_datetime(filtered_df[date_column], errors='coerce')

                    # Filter by date range if available
                    if start_date and end_date:
                        # Convert start_date and end_date to datetime.date if they're datetime objects
                        if isinstance(start_date, datetime.datetime):
                            start_date = start_date.date()
                        if isinstance(end_date, datetime.datetime):
                            end_date = end_date.date()

                        # Apply date filter
                        date_filtered_df = filtered_df[
                            (filtered_df[date_column].dt.date >= start_date) &
                            (filtered_df[date_column].dt.date <= end_date)
                        ]
                    else:
                        date_filtered_df = filtered_df

                    # Create a copy to avoid modifying the original dataframe
                    plot_df = date_filtered_df.copy()

                    # Create a date column for grouping if it doesn't exist
                    if 'date' not in plot_df.columns:
                        plot_df['date'] = plot_df[date_column].dt.date

                    # Group by date with proper aggregation
                    agg_dict = {}

                    # Determine which columns to aggregate
                    if 'total_sent' in plot_df.columns:
                        agg_dict['total_sent'] = 'sum'
                    elif 'user_email' in plot_df.columns:
                        # Count emails as a proxy for sent
                        plot_df['email_count'] = 1
                        agg_dict['email_count'] = 'sum'

                    if 'total_opened' in plot_df.columns:
                        agg_dict['total_opened'] = 'sum'
                    elif 'Open_Time' in plot_df.columns:
                        agg_dict['Open_Time'] = lambda x: x.notna().sum()
                    elif 'Last_Open_Time' in plot_df.columns:
                        agg_dict['Last_Open_Time'] = lambda x: x.notna().sum()

                    if 'total_clicked' in plot_df.columns:
                        agg_dict['total_clicked'] = 'sum'
                    elif 'Click_Time' in plot_df.columns:
                        agg_dict['Click_Time'] = lambda x: x.notna().sum()
                    elif 'Last_Click_Time' in plot_df.columns:
                        agg_dict['Last_Click_Time'] = lambda x: x.notna().sum()

                    # Group by date
                    grouped_df = plot_df.groupby('date').agg(agg_dict).reset_index()

                    # Convert date to datetime for plotting
                    grouped_df['date'] = pd.to_datetime(grouped_df['date'])

                    # Create a simple line chart for the selected metric
                    fig = go.Figure()

                    # Determine which column to use for each metric based on what's available
                    sent_col = None
                    if 'total_sent' in grouped_df.columns:
                        sent_col = 'total_sent'
                    elif 'email_count' in grouped_df.columns:
                        sent_col = 'email_count'

                    open_col = None
                    if 'total_opened' in grouped_df.columns:
                        open_col = 'total_opened'
                    elif 'Open_Time' in grouped_df.columns:
                        open_col = 'Open_Time'
                    elif 'Last_Open_Time' in grouped_df.columns:
                        open_col = 'Last_Open_Time'

                    click_col = None
                    if 'total_clicked' in grouped_df.columns:
                        click_col = 'total_clicked'
                    elif 'Click_Time' in grouped_df.columns:
                        click_col = 'Click_Time'
                    elif 'Last_Click_Time' in grouped_df.columns:
                        click_col = 'Last_Click_Time'

                    # Map selected metric to column name
                    metric_column = None
                    if selected_metric == 'Total Sent' and sent_col:
                        metric_column = sent_col
                    elif selected_metric == 'Total Opens' and open_col:
                        metric_column = open_col
                    elif selected_metric == 'Total Clicks' and click_col:
                        metric_column = click_col

                    # Map selected metric to color
                    metric_color = {
                        'Total Sent': '#1f77b4',
                        'Total Opens': '#ff7f0e',
                        'Total Clicks': '#2ca02c'
                    }[selected_metric]

                    # Add trace for selected metric if the column exists
                    if metric_column and metric_column in grouped_df.columns:
                        fig.add_trace(
                            go.Scatter(
                                x=grouped_df['date'],
                                y=grouped_df[metric_column],
                                name=selected_metric,
                                marker_color=metric_color,
                                mode='lines+markers',
                                line=dict(width=3)
                            )
                        )
                    else:
                        # If the metric column doesn't exist, show a message
                        st.warning(f"Data for {selected_metric} is not available in the uploaded file.")

                    # Add figure title
                    fig.update_layout(
                        title_text=f"{selected_metric} Over Time",
                        hovermode="x unified",
                        height=500,
                        margin=dict(l=20, r=20, t=50, b=20),
                        xaxis=dict(
                            title=dict(
                                text="Date",
                                font=dict(size=14)
                            ),
                            tickfont=dict(size=12)
                        ),
                        yaxis=dict(
                            title=dict(
                                text=selected_metric,
                                font=dict(size=14)
                            ),
                            tickfont=dict(size=12)
                        )
                    )

                    # Display the chart
                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.warning("Date information is missing. Cannot create time-based visualizations.")

                # Channel-specific metrics
                if channel == "Email" or channel == "All Channels":
                    st.markdown("### 📧 Email Metrics")

                    # Check if we have campaign summary data
                    if has_campaign_summary:
                        # Use metrics from campaign summary
                        summary = st.session_state.campaign_summary
                        total_sent = summary.get("total_emails", 0)
                        total_opened = summary.get("open_count", 0)
                        total_clicked = summary.get("click_count", 0)
                        open_rate = summary.get("open_rate", 0)
                        click_rate = summary.get("click_rate", 0)
                    else:
                        # Calculate email metrics based on available columns
                        # For sent emails
                        total_sent = 0
                        if 'total_sent' in filtered_df.columns:
                            total_sent = filtered_df['total_sent'].sum()
                        elif 'user_email' in filtered_df.columns:
                            total_sent = len(filtered_df['user_email'])

                        # For opened emails
                        total_opened = 0
                        if 'total_opened' in filtered_df.columns:
                            total_opened = filtered_df['total_opened'].sum()
                        elif 'Open_Time' in filtered_df.columns:
                            total_opened = filtered_df['Open_Time'].notna().sum()
                        elif 'Last_Open_Time' in filtered_df.columns:
                            total_opened = filtered_df['Last_Open_Time'].notna().sum()

                        # For clicked emails
                        total_clicked = 0
                        if 'total_clicked' in filtered_df.columns:
                            total_clicked = filtered_df['total_clicked'].sum()
                        elif 'Click_Time' in filtered_df.columns:
                            total_clicked = filtered_df['Click_Time'].notna().sum()
                        elif 'Last_Click_Time' in filtered_df.columns:
                            total_clicked = filtered_df['Last_Click_Time'].notna().sum()

                        # Calculate rates
                        open_rate = (total_opened / total_sent) * 100 if total_sent > 0 else 0
                        click_rate = (total_clicked / total_sent) * 100 if total_sent > 0 else 0

                    # Display email metrics in columns
                    email_col1, email_col2 = st.columns(2)

                    with email_col1:
                        # Open rate
                        if open_rate > 25:
                            open_rate_color = "green"
                            open_rate_context = "Excellent! Above industry average"
                        elif open_rate > 15:
                            open_rate_color = "orange"
                            open_rate_context = "Good, near industry average"
                        else:
                            open_rate_color = "red"
                            open_rate_context = "Below industry average"

                        st.metric("Open Rate", f"{open_rate:.2f}%", delta=f"{open_rate - 20:.2f}%" if open_rate != 20 else None, delta_color="normal")
                        st.markdown(f"<div style='font-size: 0.8em; color: {open_rate_color};'>{open_rate_context}</div>", unsafe_allow_html=True)

                    with email_col2:
                        # Click rate
                        if click_rate > 5:
                            click_rate_color = "green"
                            click_rate_context = "Excellent! Above industry average"
                        elif click_rate > 2:
                            click_rate_color = "orange"
                            click_rate_context = "Good, near industry average"
                        else:
                            click_rate_color = "red"
                            click_rate_context = "Below industry average"

                        st.metric("Click Rate", f"{click_rate:.2f}%", delta=f"{click_rate - 2.5:.2f}%" if click_rate != 2.5 else None, delta_color="normal")
                        st.markdown(f"<div style='font-size: 0.8em; color: {click_rate_color};'>{click_rate_context}</div>", unsafe_allow_html=True)

                    # Best Performing Email Copies and Generate New Base Mail Templates sections moved to Insights tab

                # Add a visual divider
                st.markdown("<hr style='margin: 15px 0; height: 1px; border: none; background-color: #f0f0f5;'>", unsafe_allow_html=True)

                # Add User Funnel Section (moved to the end)
                st.markdown("### 🔄 Funnel Health")
                st.markdown("Visualize the conversion funnel from audience to leads to sales.")

                # We'll use direct file paths instead of DataIntegration
                # This comment replaces the DataIntegration initialization

                # Get all product options from the data
                product_options = []

                # Add products from the uploaded data
                if 'Matched_Product' in df.columns:
                    product_options.extend(df['Matched_Product'].dropna().unique())
                elif 'product' in df.columns:
                    product_options.extend(df['product'].dropna().unique())
                elif 'offering' in df.columns:
                    product_options.extend(df['offering'].dropna().unique())

                # Create a list of unique product options
                product_options = list(set([p for p in product_options if p]))
                leads_product_options = ["All Products"] + product_options

                # Initialize session state for funnel product filter if not already done
                if 'leads_product_filter' not in st.session_state:
                    st.session_state.leads_product_filter = "All Products"

                # Ensure stored product is in the available options
                stored_product = st.session_state.leads_product_filter
                if stored_product not in leads_product_options:
                    stored_product = "All Products"

                # Product filter for funnel
                leads_selected_product = st.selectbox(
                    "Filter by Product",
                    options=leads_product_options,
                    key="leads_product_filter",
                    index=leads_product_options.index(stored_product)
                )

                try:
                    # Import os module locally to ensure it's available in this scope
                    import os

                    # Import Path to handle home directory paths
                    from pathlib import Path

                    # Get home directory
                    home_dir = str(Path.home())

                    # Define base path with home directory
                    base_path = os.path.join(home_dir, "openengage/data/Sample Data For Mass Generation")

                    # Load actual leads data
                    bb_leads_path = os.path.join(base_path, "BB_Leads.csv")
                    genai_leads_path = os.path.join(base_path, "Pinnacle_Leads.csv")
                    agenticai_leads_path = os.path.join(base_path, "AgenticAI_Leads.csv")

                    # Load actual sales/buyers data
                    bb_buyers_path = os.path.join(base_path, "BBBuyers.csv")
                    genai_buyers_path = os.path.join(base_path, "GenAI Buyers.csv")
                    agenticai_buyers_path = os.path.join(base_path, "AgenticAI Buyers.csv")

                    # Check if the files exist and load them
                    leads_dfs = []
                    if os.path.exists(bb_leads_path):
                        bb_leads_df = pd.read_csv(bb_leads_path)
                        bb_leads_df['product'] = 'Certified AI/ML BlackBelt Plus Program'
                        leads_dfs.append(bb_leads_df)

                    if os.path.exists(genai_leads_path):
                        genai_leads_df = pd.read_csv(genai_leads_path)
                        genai_leads_df['product'] = 'GenAI Pinnacle Plus Program'
                        leads_dfs.append(genai_leads_df)

                    if os.path.exists(agenticai_leads_path):
                        agenticai_leads_df = pd.read_csv(agenticai_leads_path)
                        agenticai_leads_df['product'] = 'Agentic AI Pioneer Program'
                        leads_dfs.append(agenticai_leads_df)

                    # Combine all leads data
                    if leads_dfs:
                        leads_df = pd.concat(leads_dfs, ignore_index=True)
                        # Standardize email column name if needed
                        if 'email' in leads_df.columns and 'user_email' not in leads_df.columns:
                            leads_df['user_email'] = leads_df['email']
                    else:
                        leads_df = pd.DataFrame(columns=['user_email', 'product'])

                    # Load sales/buyers data
                    sales_dfs = []
                    # Using the os module imported above
                    if os.path.exists(bb_buyers_path):
                        bb_buyers_df = pd.read_csv(bb_buyers_path)
                        bb_buyers_df['product'] = 'Certified AI/ML BlackBelt Plus Program'
                        # Standardize email column name
                        if 'Email_ID' in bb_buyers_df.columns and 'user_email' not in bb_buyers_df.columns:
                            bb_buyers_df['user_email'] = bb_buyers_df['Email_ID']
                        sales_dfs.append(bb_buyers_df)

                    if os.path.exists(genai_buyers_path):
                        genai_buyers_df = pd.read_csv(genai_buyers_path)
                        genai_buyers_df['product'] = 'GenAI Pinnacle Plus Program'
                        # Standardize email column name
                        if 'email' in genai_buyers_df.columns and 'user_email' not in genai_buyers_df.columns:
                            genai_buyers_df['user_email'] = genai_buyers_df['email']
                        sales_dfs.append(genai_buyers_df)

                    if os.path.exists(agenticai_buyers_path):
                        agenticai_buyers_df = pd.read_csv(agenticai_buyers_path)
                        agenticai_buyers_df['product'] = 'Agentic AI Pioneer Program'
                        # Standardize email column name
                        if 'Email_ID' in agenticai_buyers_df.columns and 'user_email' not in agenticai_buyers_df.columns:
                            agenticai_buyers_df['user_email'] = agenticai_buyers_df['Email_ID']
                        sales_dfs.append(agenticai_buyers_df)

                    # Combine all sales data
                    if sales_dfs:
                        sales_df = pd.concat(sales_dfs, ignore_index=True)
                    else:
                        sales_df = pd.DataFrame(columns=['user_email', 'product'])

                    # Filter audience data by product if selected
                    filtered_audience_df = df
                    if leads_selected_product != "All Products":
                        if 'offering' in df.columns:
                            filtered_audience_df = df[df['offering'] == leads_selected_product]
                        elif 'product' in df.columns:
                            filtered_audience_df = df[df['product'] == leads_selected_product]
                        elif 'Matched_Product' in df.columns:
                            filtered_audience_df = df[df['Matched_Product'] == leads_selected_product]

                    # Get unique counts for audience
                    audience_count = filtered_audience_df['user_email'].nunique()
                    audience_emails = set(filtered_audience_df['user_email'].str.lower().str.strip())

                    # Filter leads data by product if selected
                    filtered_leads_df = leads_df
                    if leads_selected_product != "All Products" and 'product' in leads_df.columns:
                        filtered_leads_df = leads_df[leads_df['product'] == leads_selected_product]

                    # Filter sales data by product if selected
                    filtered_sales_df = sales_df
                    if leads_selected_product != "All Products" and 'product' in sales_df.columns:
                        filtered_sales_df = sales_df[sales_df['product'] == leads_selected_product]

                    # Standardize email columns for matching
                    if 'user_email' in filtered_leads_df.columns:
                        filtered_leads_df['user_email'] = filtered_leads_df['user_email'].str.lower().str.strip()
                    elif 'email' in filtered_leads_df.columns:
                        filtered_leads_df['user_email'] = filtered_leads_df['email'].str.lower().str.strip()

                    if 'user_email' in filtered_sales_df.columns:
                        filtered_sales_df['user_email'] = filtered_sales_df['user_email'].str.lower().str.strip()
                    elif 'email' in filtered_sales_df.columns:
                        filtered_sales_df['user_email'] = filtered_sales_df['email'].str.lower().str.strip()
                    elif 'Email_ID' in filtered_sales_df.columns:
                        filtered_sales_df['user_email'] = filtered_sales_df['Email_ID'].str.lower().str.strip()

                    # Get leads and sales that are in the audience
                    leads_emails = set()
                    if 'user_email' in filtered_leads_df.columns:
                        leads_emails = set(filtered_leads_df['user_email'])

                    sales_emails = set()
                    if 'user_email' in filtered_sales_df.columns:
                        sales_emails = set(filtered_sales_df['user_email'])

                    # Find intersection with audience emails
                    audience_leads = audience_emails.intersection(leads_emails)
                    audience_sales = audience_emails.intersection(sales_emails)

                    # Get unique counts for leads and sales that are in the audience
                    leads_count = len(audience_leads)
                    sales_count = len(audience_sales)

                    # Define journey stages
                    journey_stages = ['New Visitor', 'Product Page Viewed', 'Product Lead Generated', 'Product Purchased']

                    # Calculate new visitors (audience members who are not leads or sales)
                    new_visitors = audience_count - leads_count - sales_count

                    # Ensure we don't have negative counts
                    new_visitors = max(0, new_visitors)

                    # Create stage counts DataFrame for the funnel chart
                    stage_counts = pd.DataFrame({
                        'stage': journey_stages,
                        'count': [
                            new_visitors,  # New Visitors
                            0,  # Product Page Viewed (placeholder, will be updated if available)
                            leads_count,  # Product Lead Generated
                            sales_count   # Product Purchased
                        ]
                    })

                    # Debug information has been removed for production

                    # Create funnel chart using the analytics module function
                    funnel_fig = create_user_journey_funnel_chart(
                        stage_counts,
                        journey_stages,
                        product_name=leads_selected_product if leads_selected_product != "All Products" else None
                    )

                    # Display the funnel chart
                    st.plotly_chart(funnel_fig, use_container_width=True)

                    # Calculate conversion rates
                    lead_conversion_rate = (leads_count / audience_count) * 100 if audience_count > 0 else 0
                    sale_conversion_rate = (sales_count / leads_count) * 100 if leads_count > 0 else 0
                    overall_conversion_rate = (sales_count / audience_count) * 100 if audience_count > 0 else 0

                    # Display conversion metrics
                    conv_col1, conv_col2, conv_col3 = st.columns(3)

                    with conv_col1:
                        st.metric("Audience to Leads", f"{lead_conversion_rate:.1f}%")
                        st.markdown("<div style='font-size: 0.8em; color: #666;'>Conversion rate</div>", unsafe_allow_html=True)

                    with conv_col2:
                        st.metric("Leads to Sales", f"{sale_conversion_rate:.1f}%")
                        st.markdown("<div style='font-size: 0.8em; color: #666;'>Conversion rate</div>", unsafe_allow_html=True)

                    with conv_col3:
                        st.metric("Overall Conversion", f"{overall_conversion_rate:.1f}%")
                        st.markdown("<div style='font-size: 0.8em; color: #666;'>Audience to Sales</div>", unsafe_allow_html=True)

                except Exception as e:
                    st.error(f"Error loading funnel data: {str(e)}")

                    # Add functional chatbot in the bottom right
                    render_chatbot(df)

            # Insights Tab
            with insights_tab:
                # Template Performance Section
                st.markdown("### 📊 Template Performance Analysis")
                
                # Check if template performance data is available in session state
                if hasattr(st.session_state, 'template_performance_df') and not st.session_state.template_performance_df.empty:
                    # Get the template performance data
                    template_df = st.session_state.template_performance_df
                    
                    # Display the template performance data
                    st.write(f"Analyzed {len(template_df)} email templates:")
                    
                    # Display pruning thresholds if available
                    if hasattr(st.session_state, 'template_thresholds') and st.session_state.template_thresholds:
                        thresholds = st.session_state.template_thresholds
                        
                        with st.expander("📉 Pruning Thresholds by Stage & Product", expanded=True):
                            st.markdown("### Templates below these thresholds are flagged for pruning (if they have 100+ sends):")
                            
                            if hasattr(st.session_state, 'template_performance_df') and not st.session_state.template_performance_df.empty:
                                # Get template pruning counts per stage and product
                                pruning_counts = {}
                                template_df = st.session_state.template_performance_df
                                
                                # Calculate pruning counts
                                stage_product_pruning = template_df[template_df['Pruning_Flag'] == 1].groupby(['Stage', 'Associated_Product']).size().reset_index(name='Pruned_Count')
                                stage_product_total = template_df.groupby(['Stage', 'Associated_Product']).size().reset_index(name='Total_Count')
                                
                                # Merge to get pruning percentages
                                pruning_stats = pd.merge(stage_product_pruning, stage_product_total, on=['Stage', 'Associated_Product'], how='right')
                                pruning_stats['Pruned_Count'] = pruning_stats['Pruned_Count'].fillna(0)
                                pruning_stats['Pruning_Percentage'] = (pruning_stats['Pruned_Count'] / pruning_stats['Total_Count'] * 100).round(1)
                                
                                # Store for display alongside thresholds
                                for _, row in pruning_stats.iterrows():
                                    key = f"{row['Stage']}:{row['Associated_Product']}"
                                    pruning_counts[key] = {
                                        'pruned': int(row['Pruned_Count']), 
                                        'total': int(row['Total_Count']), 
                                        'percentage': row['Pruning_Percentage']
                                    }
                                
                                st.markdown("#### Pruning Summary:")
                                summary_cols = st.columns([1, 1])
                                with summary_cols[0]:
                                    total_pruned = template_df['Pruning_Flag'].sum()
                                    total_templates = len(template_df)
                                    pruning_percentage = (total_pruned / total_templates * 100) if total_templates > 0 else 0
                                    st.metric("Total Templates Pruned", f"{int(total_pruned)} of {total_templates} ({pruning_percentage:.1f}%)")
                                with summary_cols[1]:
                                    st.metric("Stage-Product Combinations", len(pruning_stats))
                            
                            # Create a dataframe to display the thresholds in a table
                            threshold_data = []
                            
                            # Detailed debug printing for thresholds
                            print(f"DETAILED DEBUG UI: Thresholds object type: {type(thresholds)}")
                            print(f"DETAILED DEBUG UI: Raw thresholds contents: {thresholds}")
                            
                            for key, values in thresholds.items():
                                print(f"DETAILED DEBUG UI: Key {key} values type: {type(values)}")
                                print(f"DETAILED DEBUG UI: Key {key} raw values: {values}")
                                
                                # Parse stage and product from the combined key
                                if ':' in key:
                                    stage, product = key.split(':', 1)
                                else:
                                    # For backward compatibility with old data format
                                    stage = key
                                    product = 'All Products'
                                
                                # Get and print the raw values
                                cto_threshold = values.get('click_to_open_rate', 0)
                                open_threshold = values.get('open_rate', 0)
                                print(f"DETAILED DEBUG UI: {stage}/{product} - Raw CTO: {cto_threshold}, type: {type(cto_threshold)}")
                                print(f"DETAILED DEBUG UI: {stage}/{product} - Raw Open: {open_threshold}, type: {type(open_threshold)}")
                                
                                # Try converting to float if not already
                                try:
                                    cto_threshold = float(cto_threshold)
                                    open_threshold = float(open_threshold)
                                except (TypeError, ValueError) as e:
                                    print(f"DETAILED DEBUG UI: Conversion error: {e}")
                                    
                                # NORMALIZATION STEP: If values are extremely large (>100), assume they need to be normalized
                                # This handles both current data and any legacy data that might have the double multiplication
                                if cto_threshold > 100:
                                    print(f"DETAILED DEBUG UI: Normalizing oversized CTO value: {cto_threshold}")
                                    cto_threshold = cto_threshold / 100
                                    
                                if open_threshold > 100:
                                    print(f"DETAILED DEBUG UI: Normalizing oversized Open rate value: {open_threshold}")
                                    open_threshold = open_threshold / 100
                                
                                # Get pruning counts if available
                                pruned_count = 0
                                total_count = 0
                                pruning_pct = 0.0
                                
                                # Try to get pruning data for this stage-product combination
                                key = f"{stage}:{product}"
                                if key in pruning_counts:
                                    pruned_count = pruning_counts[key]['pruned']
                                    total_count = pruning_counts[key]['total']
                                    pruning_pct = pruning_counts[key]['percentage']
                                
                                threshold_data.append({
                                    "Stage": stage,
                                    "Product": product,
                                    "Click-to-Open Rate Threshold (%)": round(cto_threshold, 2),
                                    "Open Rate Threshold (%)": round(open_threshold, 2),
                                    "Templates Pruned": f"{pruned_count} of {total_count} ({pruning_pct:.1f}%)"
                                })
                                # Print debug info after processing
                                print(f"DETAILED DEBUG UI: After processing - Stage {stage} - CTO: {round(cto_threshold, 2)}%, Open: {round(open_threshold, 2)}%")
                                
                            if threshold_data:
                                threshold_df = pd.DataFrame(threshold_data)
                                st.dataframe(threshold_df.style.format({
                                    "Click-to-Open Rate Threshold (%)": "{:.2f}%",
                                    "Open Rate Threshold (%)": "{:.2f}%"
                                }), use_container_width=True)
                                
                                # Add explanatory text
                                st.caption("Thresholds represent the 20th percentile (bottom fifth) of metrics for each stage-product combination, calculated from templates with 100+ sends.")
                                st.info("📊 Pruning thresholds are now calculated per stage AND product for more accurate template performance evaluation. Each product's engagement patterns are considered separately.")
                            else:
                                st.info("No threshold data available")
                    
                    
                    # Create visualization using the function from analytics.py
                    from openengage.analytics import create_template_performance_chart
                    template_chart = create_template_performance_chart(template_df)
                    
                    # Display the chart
                    st.plotly_chart(template_chart, use_container_width=True)
                    
                    # Add a download button for the template performance data
                    year_month = datetime.datetime.now().strftime("%Y%m")
                    csv_path = f"data/mail_performance/templates/template_performance_{year_month}.csv"
                    from pathlib import Path
                    if Path(csv_path).exists():
                        with open(csv_path, "rb") as file:
                            st.download_button(
                                label="📥 Download Template Performance Data",
                                data=file,
                                file_name=f"template_performance_{year_month}.csv",
                                mime="text/csv",
                            )
                else:
                    st.info("No template performance data available. Click the 'Analyze Data' button to generate template performance metrics.")
                
                st.markdown("<hr style='margin: 30px 0;'>", unsafe_allow_html=True)
                # Best Performing Email Copies Section
                if 'Subject' in df.columns:
                    st.markdown("### 🏆 Best Performing Email Copies")

                    # Add product filter for best performing emails
                    product_filter_options = []

                    # Get product options from the data if available
                    if 'offering' in df.columns:
                        product_filter_options.extend(df['offering'].dropna().unique())
                    elif 'product' in df.columns:
                        product_filter_options.extend(df['product'].dropna().unique())
                    elif 'Matched_Product' in df.columns:
                        product_filter_options.extend(df['Matched_Product'].dropna().unique())
                    elif 'last_product_sent' in df.columns:
                        product_filter_options.extend(df['last_product_sent'].dropna().unique())
                    elif 'Last_Product_Sent' in df.columns:
                        product_filter_options.extend(df['Last_Product_Sent'].dropna().unique())

                    # Create a list of unique product options
                    product_filter_options = list(set([p for p in product_filter_options if p]))

                    # If no products found, show a message and return
                    if not product_filter_options:
                        st.warning("No product information found in the data. Please make sure your CSV contains product information.")
                        return

                    # Initialize session state for product filter if not already done
                    if 'best_emails_product_filter' not in st.session_state:
                        st.session_state.best_emails_product_filter = product_filter_options[0] if product_filter_options else None

                    # Ensure stored product is in the available options
                    stored_product = st.session_state.best_emails_product_filter
                    if stored_product not in product_filter_options:
                        stored_product = product_filter_options[0]

                    # Select the stored product or first product by default
                    selected_product = st.selectbox(
                        "Filter by Product",
                        options=product_filter_options,
                        key="best_emails_product_filter",
                        index=product_filter_options.index(stored_product)
                    )

                    # Filter data based on selected product
                    if 'offering' in df.columns:
                        product_filtered_df = df[df['offering'] == selected_product]
                    elif 'product' in df.columns:
                        product_filtered_df = df[df['product'] == selected_product]
                    elif 'Matched_Product' in df.columns:
                        product_filtered_df = df[df['Matched_Product'] == selected_product]
                    elif 'last_product_sent' in df.columns:
                        product_filtered_df = df[df['last_product_sent'] == selected_product]
                    elif 'Last_Product_Sent' in df.columns:
                        product_filtered_df = df[df['Last_Product_Sent'] == selected_product]
                    else:
                        product_filtered_df = df

                    # Check if we have data after filtering
                    if product_filtered_df.empty:
                        st.warning(f"No data available for product: {selected_product}")
                        return

                    # Check if template column exists (base_mails renamed as template)
                    template_column = None
                    if 'template' in product_filtered_df.columns:
                        template_column = 'template'
                    elif 'base_mails' in product_filtered_df.columns:
                        template_column = 'base_mails'
                    elif 'email-template' in product_filtered_df.columns:
                        template_column = 'email-template'
                    elif 'template-id' in product_filtered_df.columns:
                        # If we only have template-id but no template name, use template-id as the template column
                        template_column = 'template-id'

                    # Create two sections: Best Subjects (by Open Rate) and Best Email Bodies (by Pure Click Rate)
                    st.markdown("#### Best Performing Subjects (by Open Rate)")

                    # Check if we have pre-calculated subject performance metrics
                    if has_campaign_summary and 'subject_performance' in st.session_state.campaign_summary and st.session_state.campaign_summary['subject_performance']:
                        # Use pre-calculated subject performance metrics
                        subject_performance_data = st.session_state.campaign_summary['subject_performance']
                        subject_performance = pd.DataFrame(subject_performance_data)

                        # Filter by product if needed
                        if 'product' in subject_performance.columns and selected_product:
                            subject_performance = subject_performance[subject_performance['product'] == selected_product]
                    else:
                        # Calculate subject performance from filtered data using the binary approach
                        # Create binary columns for opens and sends
                        product_filtered_df['Open'] = product_filtered_df['Open_Time'].apply(lambda x: 0 if pd.isna(x) else 1) if 'Open_Time' in product_filtered_df.columns else 0

                        # For sends, use Campaign_ID if available, otherwise fall back to other columns
                        if 'Campaign_ID' in product_filtered_df.columns:
                            product_filtered_df['Sent'] = product_filtered_df['Campaign_ID'].apply(lambda x: 0 if pd.isna(x) else 1)
                        elif 'ISO_Date_Time' in product_filtered_df.columns:
                            product_filtered_df['Sent'] = product_filtered_df['ISO_Date_Time'].apply(lambda x: 0 if pd.isna(x) else 1)
                        elif 'Send_Time' in product_filtered_df.columns:
                            product_filtered_df['Sent'] = product_filtered_df['Send_Time'].apply(lambda x: 0 if pd.isna(x) else 1)
                        else:
                            # If no suitable column is available, assume all rows were sent
                            product_filtered_df['Sent'] = 1

                        # Group by subject and sum the opens and sends
                        if 'Subject' in product_filtered_df.columns:
                            subject_performance = product_filtered_df.groupby('Subject')[['Open', 'Sent']].sum().reset_index()

                            # Calculate open rate (opens / sends)
                            subject_performance['Open Rate'] = (subject_performance['Open'] / subject_performance['Sent']) * 100
                            subject_performance['Open Rate'] = subject_performance['Open Rate'].fillna(0)

                            # Rename columns for consistency with the rest of the code
                            subject_performance = subject_performance.rename(columns={
                                'Open': 'Open_Time',
                                'Sent': 'user_email'
                            })
                        else:
                            # If no Subject column is available, create an empty DataFrame
                            subject_performance = pd.DataFrame(columns=['Subject', 'Open_Time', 'user_email', 'Open Rate'])

                    # Sort by open rate and get top 3
                    if not subject_performance.empty and 'Open Rate' in subject_performance.columns:
                        top_subjects = subject_performance.sort_values('Open Rate', ascending=False).head(3)
                    else:
                        # Create an empty DataFrame with the required columns
                        top_subjects = pd.DataFrame(columns=['Subject', 'Open Rate'])

                    # Add sent date if available
                    if 'Send_Time' in product_filtered_df.columns and not top_subjects.empty:
                        send_dates = product_filtered_df.groupby('Subject')['Send_Time'].max().reset_index()
                        send_dates.columns = ['Subject', 'Sent Date']
                        top_subjects = top_subjects.merge(send_dates, on='Subject', how='left')
                    else:
                        if not top_subjects.empty:
                            top_subjects['Sent Date'] = 'Unknown'

                    # Prepare data for display - show only Subject and Open Rate
                    if not top_subjects.empty:
                        subject_display_data = top_subjects[['Subject', 'Open Rate']]
                    else:
                        subject_display_data = pd.DataFrame(columns=['Subject', 'Open Rate'])

                    # Display the best performing subjects
                    st.markdown("**Top 3 Best Performing Subjects by Open Rate:**")
                    if not subject_display_data.empty:
                        # Add rank column
                        subject_display_data['Rank'] = range(1, len(subject_display_data) + 1)
                        # Reorder columns to show rank first
                        subject_display_data = subject_display_data[['Rank', 'Subject', 'Open Rate']]

                        st.dataframe(
                            subject_display_data.style.format({
                                'Open Rate': '{:.1f}%'
                            }),
                            hide_index=True,
                            use_container_width=True
                        )
                    else:
                        st.info("No subject performance data available.")

                    # Now create the Best Email Bodies section (by Pure Click Rate)
                    st.markdown("#### Best Performing Email Bodies (by Pure Click Rate)")

                    # Check which click time column to use
                    click_time_column = None
                    if 'Click_Time' in product_filtered_df.columns:
                        click_time_column = 'Click_Time'
                    elif 'Last_Click_Time' in product_filtered_df.columns:
                        click_time_column = 'Last_Click_Time'

                    # Check which open time column to use
                    open_time_column = None
                    if 'Open_Time' in product_filtered_df.columns:
                        open_time_column = 'Open_Time'
                    elif 'Last_Open_Time' in product_filtered_df.columns:
                        open_time_column = 'Last_Open_Time'

                    # Check if we have pre-calculated template performance metrics
                    if has_campaign_summary and 'template_performance' in st.session_state.campaign_summary and st.session_state.campaign_summary['template_performance']:
                        # Use pre-calculated template performance metrics
                        template_performance_data = st.session_state.campaign_summary['template_performance']
                        template_performance = pd.DataFrame(template_performance_data)

                        # Filter by product if needed
                        if 'product' in template_performance.columns and selected_product:
                            template_performance = template_performance[template_performance['product'] == selected_product]

                        # Sort by pure click rate and get top 3
                        if 'Pure Click Rate' in template_performance.columns:
                            top_templates = template_performance.sort_values('Pure Click Rate', ascending=False).head(3)

                            # Check if template-id column exists
                            template_id_column = None
                            if 'template-id' in product_filtered_df.columns:
                                template_id_column = 'template-id'

                            # Prepare display data based on available columns
                            if template_id_column and 'Template ID' in top_templates.columns:
                                display_columns = [template_column, 'Template ID', 'Subject', 'Pure Click Rate', 'Open Rate']
                                body_display_data = top_templates[display_columns]
                                body_display_data.columns = ['Template', 'Template ID', 'Sample Subject', 'Pure Click Rate', 'Open Rate']
                            elif template_column and 'Subject' in top_templates.columns:
                                body_display_data = top_templates[[template_column, 'Subject', 'Pure Click Rate', 'Open Rate']]
                                body_display_data.columns = ['Template', 'Sample Subject', 'Pure Click Rate', 'Open Rate']
                            else:
                                # Fallback if columns are missing
                                body_display_data = top_templates
                        else:
                            # If Pure Click Rate is missing, calculate it from the available data
                            body_display_data = None
                    elif template_column and click_time_column and open_time_column:
                        # Calculate template performance from filtered data
                        # Make sure the click time column is in datetime format
                        product_filtered_df[click_time_column] = pd.to_datetime(product_filtered_df[click_time_column], errors='coerce')

                        # Check if template-id column exists
                        template_id_column = None
                        if 'template-id' in product_filtered_df.columns:
                            template_id_column = 'template-id'

                        # Group by template
                        template_performance = product_filtered_df.groupby(template_column).agg({
                            'user_email': 'count',
                            open_time_column: lambda x: x.notna().sum(),
                            click_time_column: lambda x: x.notna().sum()
                        }).reset_index()

                        # Calculate pure click rate (clicks/opens)
                        template_performance['Pure Click Rate'] = (template_performance[click_time_column] / template_performance[open_time_column]) * 100
                        # Handle division by zero and NaN values
                        template_performance['Pure Click Rate'] = template_performance['Pure Click Rate'].fillna(0)
                        # Replace infinity values with 0
                        template_performance.loc[template_performance['Pure Click Rate'] == float('inf'), 'Pure Click Rate'] = 0

                        # Sort by pure click rate and get top 3
                        top_templates = template_performance.sort_values('Pure Click Rate', ascending=False).head(3)

                        # Add open rate for reference
                        top_templates['Open Rate'] = (top_templates[open_time_column] / top_templates['user_email']) * 100

                        # Add sent date if available
                        if 'Send_Time' in product_filtered_df.columns:
                            send_dates = product_filtered_df.groupby(template_column)['Send_Time'].max().reset_index()
                            send_dates.columns = [template_column, 'Sent Date']
                            top_templates = top_templates.merge(send_dates, on=template_column, how='left')
                        else:
                            top_templates['Sent Date'] = 'Unknown'

                        # Get a sample subject for each template
                        if 'Subject' in product_filtered_df.columns:
                            sample_subjects = product_filtered_df.groupby(template_column)['Subject'].first().reset_index()
                            top_templates = top_templates.merge(sample_subjects, on=template_column, how='left')

                        # Add template-id if available
                        if template_id_column:
                            # Get the most common template-id for each template
                            template_ids = product_filtered_df.groupby(template_column)[template_id_column].agg(
                                lambda x: x.mode()[0] if not x.mode().empty else ''
                            ).reset_index()
                            template_ids.columns = [template_column, 'Template ID']
                            top_templates = top_templates.merge(template_ids, on=template_column, how='left')

                            # Prepare data for display with template ID - remove Sent Date column
                            display_columns = [template_column, 'Template ID', 'Subject', 'Pure Click Rate', 'Open Rate']
                            body_display_data = top_templates[display_columns]
                            body_display_data.columns = ['Template', 'Template ID', 'Sample Subject', 'Pure Click Rate', 'Open Rate']
                        else:
                            # Prepare data for display without template ID - remove Sent Date column
                            body_display_data = top_templates[[template_column, 'Subject', 'Pure Click Rate', 'Open Rate']]
                            body_display_data.columns = ['Template', 'Sample Subject', 'Pure Click Rate', 'Open Rate']

                    elif 'personalized_mails' in product_filtered_df.columns and click_time_column and open_time_column:
                        # Check if we have pre-calculated personalized mail performance metrics
                        if has_campaign_summary and 'personalized_mail_performance' in st.session_state.campaign_summary and st.session_state.campaign_summary['personalized_mail_performance']:
                            # Use pre-calculated personalized mail performance metrics
                            mail_performance_data = st.session_state.campaign_summary['personalized_mail_performance']
                            mail_performance = pd.DataFrame(mail_performance_data)

                            # Filter by product if needed
                            if 'product' in mail_performance.columns and selected_product:
                                mail_performance = mail_performance[mail_performance['product'] == selected_product]

                            # Sort by pure click rate and get top 3
                            if 'Pure Click Rate' in mail_performance.columns:
                                top_mails = mail_performance.sort_values('Pure Click Rate', ascending=False).head(3)

                                # Prepare display data based on available columns
                                if 'Subject' in top_mails.columns:
                                    body_display_data = top_mails[['personalized_mails', 'Subject', 'Pure Click Rate', 'Open Rate']]
                                    body_display_data.columns = ['Personalized Mail', 'Sample Subject', 'Pure Click Rate', 'Open Rate']
                                else:
                                    # Fallback if columns are missing
                                    body_display_data = top_mails
                            else:
                                # If Pure Click Rate is missing, calculate it from the available data
                                body_display_data = None
                        else:
                            # Calculate personalized mail performance from filtered data
                            # Group by personalized_mails
                            mail_performance = product_filtered_df.groupby('personalized_mails').agg({
                                'user_email': 'count',
                                open_time_column: lambda x: x.notna().sum(),
                                click_time_column: lambda x: x.notna().sum()
                            }).reset_index()

                            # Calculate pure click rate (clicks/opens)
                            mail_performance['Pure Click Rate'] = (mail_performance[click_time_column] / mail_performance[open_time_column]) * 100
                            # Handle division by zero and NaN values
                            mail_performance['Pure Click Rate'] = mail_performance['Pure Click Rate'].fillna(0)
                            # Replace infinity values with 0
                            mail_performance.loc[mail_performance['Pure Click Rate'] == float('inf'), 'Pure Click Rate'] = 0

                            # Sort by pure click rate and get top 3
                            top_mails = mail_performance.sort_values('Pure Click Rate', ascending=False).head(3)

                            # Add open rate for reference
                            top_mails['Open Rate'] = (top_mails[open_time_column] / top_mails['user_email']) * 100

                            # Add sent date if available
                            if 'Send_Time' in product_filtered_df.columns:
                                send_dates = product_filtered_df.groupby('personalized_mails')['Send_Time'].max().reset_index()
                                send_dates.columns = ['personalized_mails', 'Sent Date']
                                top_mails = top_mails.merge(send_dates, on='personalized_mails', how='left')
                            else:
                                top_mails['Sent Date'] = 'Unknown'

                            # Get a sample subject for each mail
                            if 'Subject' in product_filtered_df.columns:
                                sample_subjects = product_filtered_df.groupby('personalized_mails')['Subject'].first().reset_index()
                                top_mails = top_mails.merge(sample_subjects, on='personalized_mails', how='left')

                            # Prepare data for display - remove Sent Date column
                            body_display_data = top_mails[['personalized_mails', 'Subject', 'Pure Click Rate', 'Open Rate']]
                            body_display_data.columns = ['Personalized Mail', 'Sample Subject', 'Pure Click Rate', 'Open Rate']

                    else:
                        # If we don't have click data, use the subject performance as a fallback
                        st.warning("Click data not available. Cannot calculate Pure Click Rate.")
                        body_display_data = None

                    # Display the best performing email bodies if available
                    if body_display_data is not None:
                        st.dataframe(
                            body_display_data.style.format({
                                'Pure Click Rate': '{:.1f}%',
                                'Open Rate': '{:.1f}%'
                            }),
                            hide_index=True,
                            use_container_width=True
                        )

                    # Store the top subjects and templates/mails for use in the email generation section
                    st.session_state.top_subjects = top_subjects
                    if 'top_templates' in locals():
                        st.session_state.top_templates = top_templates
                    elif 'top_mails' in locals():
                        st.session_state.top_mails = top_mails

                    # Add an expander to show the full content of the best templates/subjects
                    with st.expander("View Full Content of Best Performing Emails", expanded=False):
                        if template_column:
                            for i, row in top_templates.iterrows():
                                # Show template ID if available
                                if 'Template ID' in row:
                                    st.markdown(f"### Template {i+1}: {row[template_column]}")
                                    st.markdown(f"**Template ID:** {row['Template ID']}")
                                else:
                                    st.markdown(f"### Template {i+1}: {row[template_column]}")

                                st.markdown("**Sample Subject:**")
                                st.markdown(f"> {row['Subject']}")

                                # Show personalized examples if available
                                if 'personalized_mails' in product_filtered_df.columns:
                                    # Get a few examples of personalized mails for this template
                                    personalized_examples = product_filtered_df[product_filtered_df[template_column] == row[template_column]]['personalized_mails'].head(2).tolist()
                                    if personalized_examples:
                                        st.markdown("**Personalized Examples:**")
                                        for j, example in enumerate(personalized_examples):
                                            st.markdown(f"Example {j+1}:")
                                            st.markdown(f"> {example}")

                                st.markdown("---")
                        else:
                            for i, row in top_subjects.iterrows():
                                st.markdown(f"### Email {i+1}: {row['Subject']}")

                                # Show personalized content if available
                                if 'personalized_mails' in product_filtered_df.columns:
                                    st.markdown("**Personalized Content:**")
                                    st.markdown(f"> {row['personalized_mails']}")

                                st.markdown("---")

                    # Add section for generating new base mail templates using GPT-4o-mini
                    st.markdown("### 🤖 Generate New Base Mail Templates")
                    st.markdown("Use AI to generate new base mail templates based on your best performing emails. These templates will include placeholders like {{name}} and {{product}} that can be personalized later.")

                    # Product filter for generation
                    gen_product_filter_options = []

                    # Get product options from the data if available
                    if 'offering' in df.columns:
                        gen_product_filter_options.extend(df['offering'].unique())
                    elif 'product' in df.columns:
                        gen_product_filter_options.extend(df['product'].unique())
                    elif 'Matched_Product' in df.columns:
                        gen_product_filter_options.extend(df['Matched_Product'].unique())

                    # Default to the same product as the best performing emails filter
                    default_product_index = 0
                    if selected_product in gen_product_filter_options:
                        default_product_index = gen_product_filter_options.index(selected_product)

                    # Product selection for generation
                    gen_selected_product = st.selectbox(
                        "Product for New Email Copies",
                        options=gen_product_filter_options,
                        key="generate_emails_product_filter",
                        index=default_product_index
                    )

                    # Number of emails to generate
                    num_emails = st.slider("Number of emails to generate", min_value=3, max_value=5, value=3, step=1)

                    # Button to generate new base mail templates
                    if st.button("Generate New Base Mail Templates", key="generate_emails_button"):
                        with st.spinner("Generating new base mail templates..."):
                            try:
                                # Import the necessary modules for OpenAI API
                                import os  # Import os locally to ensure it's available in this scope
                                from openai import OpenAI
                                from dotenv import load_dotenv

                                # Load environment variables
                                load_dotenv()

                                # Initialize OpenAI client
                                api_key = os.getenv("OPENAI_API_KEY")
                                if not api_key:
                                    st.error("OpenAI API key not found. Please set the OPENAI_API_KEY environment variable.")
                                    return

                                client = OpenAI(api_key=api_key)

                                # Prepare the prompt based on the best performing subjects and email bodies
                                prompt = f"Based on the following best performing email subjects and bodies for {gen_selected_product}, generate {num_emails} new email templates with subject lines.\n\n"

                                # Add best performing subjects (by open rate)
                                prompt += "BEST PERFORMING SUBJECTS (by Open Rate):\n"
                                if hasattr(st.session_state, 'top_subjects'):
                                    for i, row in st.session_state.top_subjects.iterrows():
                                        prompt += f"Subject {i+1}: {row['Subject']} (Open Rate: {row['Open Rate']:.1f}%)\n"

                                prompt += "\n"

                                # Add best performing email bodies (by pure click rate)
                                prompt += "BEST PERFORMING EMAIL BODIES (by Pure Click Rate):\n"

                                # Prioritize using base_mails (templates) for generating new base mails
                                if hasattr(st.session_state, 'top_templates') and template_column:
                                    prompt += "USING BASE MAIL TEMPLATES:\n"
                                    for i, row in st.session_state.top_templates.iterrows():
                                        prompt += f"Template {i+1}:\n"
                                        # Include template ID if available
                                        if 'Template ID' in row:
                                            prompt += f"Template ID: {row['Template ID']}\n"
                                        prompt += f"Base Mail Template: {row[template_column]}\n"
                                        prompt += f"Pure Click Rate: {row['Pure Click Rate']:.1f}%\n\n"
                                elif hasattr(st.session_state, 'top_mails'):
                                    prompt += "USING PERSONALIZED MAILS (since base mail templates not available):\n"
                                    for i, row in st.session_state.top_mails.iterrows():
                                        prompt += f"Email Body {i+1}:\n"
                                        prompt += f"Content: {row['personalized_mails']}\n"
                                        prompt += f"Pure Click Rate: {row['Pure Click Rate']:.1f}%\n\n"

                                prompt += f"\nPlease generate {num_emails} new BASE MAIL TEMPLATES for {gen_selected_product}. Each template should include:\n"
                                prompt += f"1. A subject line inspired by the best performing subjects (optimize for open rate)\n"
                                prompt += f"2. A base mail template with placeholders like {{name}} and {{product}} where appropriate\n"
                                prompt += f"3. Focus on creating templates that can be personalized later, not fully personalized emails\n"
                                prompt += f"4. Optimize the template structure for high click-through rates based on the best performing examples\n"
                                prompt += f"Make them engaging and similar in style to the best performing ones, but with fresh and innovative approaches."

                                # Call GPT-4o-mini API using the new OpenAI client
                                response = client.chat.completions.create(
                                    model="gpt-4o-mini",
                                    messages=[
                                        {"role": "system", "content": "You are an expert email marketer who creates engaging and high-converting email templates."},
                                        {"role": "user", "content": prompt}
                                    ],
                                    max_tokens=1000,
                                    temperature=0.7
                                )

                                # Extract the generated content
                                generated_content = response.choices[0].message.content

                                # Display the generated base mail templates
                                st.markdown("## Generated Base Mail Templates")
                                st.markdown(generated_content)

                                # Add a download button for the generated content
                                st.download_button(
                                    label="Download Generated Base Mail Templates",
                                    data=generated_content,
                                    file_name="generated_base_mail_templates.txt",
                                    mime="text/plain"
                                )

                            except Exception as e:
                                st.error(f"Error generating base mail templates: {str(e)}")
                                st.markdown("Make sure you have set up the OpenAI API key in your environment variables.")
                                st.markdown("You can add it to a .env file with the format: `OPENAI_API_KEY=your_api_key_here`")
                else:
                    st.info("Cannot display best performing email copies. The CSV file must contain 'Subject' and 'Open_Time' columns.")

                # Campaign Outcome Prediction Section
                st.markdown("### 🔮 Campaign Outcome Prediction")

                st.markdown("""
                Our AI-powered prediction engine forecasts your campaign's likely performance based on historical data.

                Click below to predict your latest campaign's performance:
                """)

                # Add a button to predict the latest campaign
                predict_campaign = st.button("🔮 Predict Latest Campaign Performance", help="Predict performance metrics for the latest campaign", key="predict_campaign_button")

                if predict_campaign or ('campaign_prediction' in st.session_state and st.session_state.campaign_prediction):
                    with st.spinner("Predicting campaign outcomes..."):
                        # Import necessary modules
                        from core.campaign_predictor import CampaignPredictor
                        from analytics import create_campaign_prediction_visualization

                        # Initialize predictor
                        predictor = CampaignPredictor()

                        # Get the latest campaign file
                        campaign_files = os.listdir("data/campaign_results/")
                        campaign_files = [f for f in campaign_files if f.endswith('.csv')]

                        if campaign_files:
                            # Sort by date (assuming filename format includes date)
                            campaign_files.sort(reverse=True)
                            latest_campaign = os.path.join("data/campaign_results/", campaign_files[0])

                            # Predict campaign performance
                            prediction_results = predictor.predict_campaign_performance(latest_campaign)

                            # Store prediction results in session state
                            st.session_state.campaign_prediction = prediction_results

                            if prediction_results['success']:
                                # Create visualization
                                fig = create_campaign_prediction_visualization(prediction_results)

                                # Display visualization
                                st.plotly_chart(fig, use_container_width=True)

                                # Add concise context to the prediction
                                st.markdown("""
                                **Color Guide**:
                                🟢 **Green** = Above benchmarks |
                                🟡 **Yellow** = Near benchmarks |
                                🔴 **Red** = Below benchmarks

                                *Black line shows your historical average*
                                """)

                                # Display prediction details with enhanced narrative
                                with st.expander("📊 Prediction Details & Insights", expanded=False):
                                    col1, col2 = st.columns([1, 1])

                                    with col1:
                                        st.markdown("#### 📧 Campaign Information")
                                        st.markdown(f"**Campaign:** {prediction_results['campaign_name']}")
                                        st.markdown(f"**Date:** {prediction_results['campaign_date']}")
                                        st.markdown(f"**Recipients:** {prediction_results['campaign_size']:,}")

                                    with col2:
                                        st.markdown("#### 📈 Predicted Metrics")
                                        metrics_df = pd.DataFrame({
                                            'Metric': ['Open Rate', 'Click Rate', 'Unsubscribe Rate'],
                                            'Prediction': [
                                                f"{prediction_results['predictions'].get('open_rate', 0):.1f}%",
                                                f"{prediction_results['predictions'].get('click_rate', 0):.1f}%",
                                                f"{prediction_results['predictions'].get('unsub_rate', 0):.2f}%"
                                            ]
                                        })
                                        st.dataframe(metrics_df, hide_index=True)

                                    # Show model accuracy metrics if available
                                    if 'training_metrics' in prediction_results:
                                        st.markdown("**Model Performance:**")
                                        for metric, values in prediction_results['training_metrics'].items():
                                            if 'r2' in values:
                                                accuracy = values['r2'] * 100
                                                st.markdown(f"- {metric.replace('_', ' ').title()}: {accuracy:.1f}%")
                            else:
                                st.error(f"Failed to predict campaign performance: {prediction_results.get('error', 'Unknown error')}")
                        else:
                            st.warning("No campaign files found. Please send a campaign first.")

    # If neither analyze button was clicked nor file was uploaded
    else:
        st.info("Please upload a CSV file or click 'Analyze' to view the analytics story.")
