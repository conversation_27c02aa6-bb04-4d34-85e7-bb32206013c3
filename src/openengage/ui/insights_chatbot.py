"""
OpenEngage - Insights Chatbot UI Component
Provides a chat interface for querying the campaign database using natural language.
"""
import streamlit as st
import sys
import os
import subprocess
import time
import re

# Add parent directory to path to import insights_agent
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from insights_agent import run_query

# Import groq for error handling
try:
    import groq
except ImportError:
    groq = None

def run_script(script_path):
    """Run a Python script as a subprocess and return its output"""
    try:
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            check=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error: {e.stderr}"

def display_insights_chatbot():
    """Display the insights chatbot interface"""
    
    # Get the absolute paths to the scripts
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    configure_db_path = os.path.join(parent_dir, 'configure_db.py')
    process_data_path = os.path.join(parent_dir, 'process_performance_data.py')
    
    # Create a container for the header and buttons
    header_container = st.container()
    
    with header_container:
        # Create a layout with columns for header and buttons
        header_col, btn1_col, btn2_col = st.columns([0.7, 0.15, 0.15])
        
        with header_col:
            # Set up page header
            st.markdown("""
                <div style='background-color: #8D06FE; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;'>
                    <h2 style='color: #FFFFFF; margin: 0; font-size: 1.5rem;'>Insights Chatbot</h2>
                    <p style='color: #FFFFFF; margin: 0.5rem 0 0 0; font-size: 1rem;'>
                        Ask questions about your campaign data using natural language
                    </p>
                </div>
            """, unsafe_allow_html=True)
        
        with btn1_col:
            # Button to run configure_db.py
            if st.button('Setup DB', key='setup_db_btn', help='Initialize database with sample data'):
                with st.spinner('Setting up database...'):
                    success, output = run_script(configure_db_path)
                    if success:
                        st.success('Database setup complete!')
                        # Add a system message to the chat history
                        if 'insights_chat_history' in st.session_state:
                            st.session_state.insights_chat_history.append({
                                "role": "assistant", 
                                "content": "✅ Database has been reset with fresh sample data. You can now query the database."
                            })
                    else:
                        st.error('Database setup failed!')
                        st.error(output)
        
        with btn2_col:
            # Button to run process_performance_data.py
            if st.button('Process Data', key='process_data_btn', help='Process performance data'):
                with st.spinner('Processing performance data...'):
                    success, output = run_script(process_data_path)
                    if success:
                        st.success('Data processing complete!')
                        # Add a system message to the chat history
                        if 'insights_chat_history' in st.session_state:
                            st.session_state.insights_chat_history.append({
                                "role": "assistant", 
                                "content": "✅ Performance data has been processed and loaded into the database. You can now query the updated data."
                            })
                    else:
                        st.error('Data processing failed!')
                        st.error(output)
    
    # Initialize chat history in session state if it doesn't exist
    if "insights_chat_history" not in st.session_state:
        st.session_state.insights_chat_history = []
    
    # Welcome message
    if not st.session_state.insights_chat_history:
        st.info("Hello! Ask me anything you want from your database.")
    
    # Display chat history
    for message in st.session_state.insights_chat_history:
        if message["role"] == "user":
            with st.chat_message("user", avatar="👤"):
                st.markdown(message["content"])
        else:
            with st.chat_message("assistant", avatar="🤖"):
                st.markdown(message["content"])
    
    # Chat input
    query = st.chat_input("Type your database query here...")
    
    # Process the query when submitted
    if query:
        # Add user message to chat history
        st.session_state.insights_chat_history.append({"role": "user", "content": query})
        
        # Display user message
        with st.chat_message("user", avatar="👤"):
            st.markdown(query)
        
        # Process query and display response
        with st.chat_message("assistant", avatar="🤖"):
            with st.spinner("Analyzing your data..."):
                # Initialize variables for retry logic
                max_retries = 3
                retry_count = 0
                retry_delay = 5  # Start with 5 seconds
                
                while retry_count <= max_retries:
                    try:
                        # Call the run_query function from insights_agent.py
                        response = run_query(query)
                        
                        # Add assistant response to chat history
                        st.session_state.insights_chat_history.append({"role": "assistant", "content": response})
                        
                        # Display the response
                        st.markdown(response)
                        break  # Success, exit the retry loop
                        
                    except Exception as e:
                        error_str = str(e)
                        
                        # Check if it's a rate limit error
                        if "rate_limit_exceeded" in error_str or "Rate limit reached" in error_str:
                            retry_count += 1
                            
                            # Extract wait time if available
                            wait_time_match = re.search(r'try again in (\d+\.?\d*)s', error_str)
                            wait_time = float(wait_time_match.group(1)) if wait_time_match else retry_delay
                            
                            # If we have more retries left, wait and try again
                            if retry_count <= max_retries:
                                retry_message = f"Rate limit reached. Waiting {wait_time:.1f} seconds and retrying... (Attempt {retry_count}/{max_retries})"
                                st.warning(retry_message)
                                time.sleep(wait_time)
                                continue
                            else:
                                error_message = "Rate limit exceeded. Maximum retry attempts reached. Please try again later."
                        else:
                            # For other errors, don't retry
                            error_message = f"Error processing your query: {error_str}"
                            break
                
                # If we've exhausted all retries or encountered a non-rate-limit error
                if retry_count > max_retries or 'error_message' in locals():
                    st.error(error_message)
                    st.session_state.insights_chat_history.append({"role": "assistant", "content": error_message})
