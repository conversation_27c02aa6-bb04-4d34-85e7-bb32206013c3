"""
Email generation functionality for OpenEngage.
"""
import os
import json
import streamlit as st
from datetime import datetime
from utils.file_utils import load_product_details, load_communication_settings, load_node_email, save_node_email,load_user_journey
from core.email_formatter import text_to_html

def generate_templates(num_templates, product_data):
    """Generate templates for all stages"""
    # Read all necessary data from files
    with open('data/preferences.json', 'r') as f:
        channel_prefs = json.load(f)

    # Get communication settings for this company
    comm_settings = load_communication_settings(product_data.get("Company_Name", ""))

    # Update settings with any modifications made in the UI
    if "modified_comm_settings" in st.session_state:
        comm_settings.update(st.session_state.modified_comm_settings)

    stages = [stage['current_stage'] for stage in load_user_journey(product_data.get("Product_Name", ""))]

    # Show progress bar
    progress_text = "Generating templates for all stages..."
    my_bar = st.progress(0, text=progress_text)

    # Load existing templates if any
    if not os.path.exists('data/templates'):
        os.makedirs('data/templates')

    # Generate templates for each stage
    total_templates = 0
    for idx, stage in enumerate(stages):
        st.write(f"### Generating templates for stage: {stage}")
        stage_file = f'data/templates/{stage.lower().replace(" ", "_")}.json'

        # Load existing templates for this stage
        templates = []
        try:
            with open(stage_file, 'r') as f:
                templates = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            templates = []

        # Generate new templates
        for i in range(num_templates):
            template_name = f"{product_data['Product_Name'].replace(' ', '_')}_{stage.replace(' ', '_')}_{len(templates) + 1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Handle separate emoji instructions for subject and body
            use_emojis_subject = comm_settings.get("use_emojis_subject", False)
            use_emojis_body = comm_settings.get("use_emojis_body", False)
            
            # For backward compatibility
            if "use_emojis" in comm_settings and "use_emojis_subject" not in comm_settings:
                use_emojis = comm_settings.get("use_emojis", False)
                use_emojis_subject = use_emojis
                use_emojis_body = use_emojis
                
            emoji_instruction = ""
            if use_emojis_subject and use_emojis_body:
                emoji_instruction = "Include relevant and engaging emojis in both the subject line and throughout the email body to make it more visually appealing and engaging."
            elif use_emojis_subject:
                emoji_instruction = "Include relevant and engaging emojis in the subject line only. Strictly DO NOT include emojis in the email body."
            elif use_emojis_body:
                emoji_instruction = "Include relevant and engaging emojis throughout the email body only. Strictly DO NOT include emojis in the subject line."
            
            # Generate template using crew
            response = st.session_state.crew.generate_campaign(
                product_data=product_data,
                channels=[comm_settings.get("utm_medium", "Email")],
                settings=comm_settings,
                stage=stage,
                instructions=emoji_instruction
            )
            
            # Process the template to enforce emoji settings
            # Parse the template response to get subject and body separately
            template = response
            subject = ""
            body = ""
            
            # Check if the template has a standard format with Subject: and Body: sections
            if "Subject:" in template:
                parts = template.split("Subject:", 1)
                if len(parts) > 1:
                    template_parts = parts[1].split("Body:", 1)
                    if len(template_parts) > 1:
                        subject = template_parts[0].strip()
                        body = template_parts[1].strip()
                    else:
                        # If no Body: tag, assume everything after Subject: is the body
                        subject = template_parts[0].strip()
                        body = ""
            
            # If we successfully parsed the template
            if subject or body:
                # Import regex for emoji detection and removal
                import re
                
                # Emoji pattern - this covers most common emoji unicode ranges
                emoji_pattern = re.compile(
                    "["                            # Start of character class
                    "\U0001F1E0-\U0001F1FF"      # Flags
                    "\U0001F300-\U0001F5FF"      # Symbols & Pictographs
                    "\U0001F600-\U0001F64F"      # Emoticons
                    "\U0001F680-\U0001F6FF"      # Transport & Map Symbols
                    "\U0001F700-\U0001F77F"      # Alchemical Symbols
                    "\U0001F780-\U0001F7FF"      # Geometric Shapes
                    "\U0001F800-\U0001F8FF"      # Supplemental Arrows-C
                    "\U0001F900-\U0001F9FF"      # Supplemental Symbols and Pictographs
                    "\U0001FA00-\U0001FA6F"      # Chess Symbols
                    "\U0001FA70-\U0001FAFF"      # Symbols and Pictographs Extended-A
                    "\U00002702-\U000027B0"      # Dingbats
                    "\U000024C2-\U0000257F"      # Enclosed characters
                    "\U00002600-\U000026FF"      # Miscellaneous
                    "\U00002700-\U000027BF"      # Dingbats
                    "\U0000FE0F"                 # Variation Selector (VS-16)
                    "\U0000200D"                 # Zero Width Joiner
                    "]", 
                    re.UNICODE
                )
                
                # Remove emojis from subject if setting is disabled
                if not use_emojis_subject and subject:
                    subject = emoji_pattern.sub('', subject)
                
                # Remove emojis from body if setting is disabled
                if not use_emojis_body and body:
                    body = emoji_pattern.sub('', body)
                
                # Reconstruct the template with enforced emoji settings
                response = f"Subject: {subject}\n\nBody: {body}"
            
            # Generate template tags focusing on features, offers, and motivation
            tag_prompt = f"""Analyze this email template and extract specific tags (1-3 words each) that describe ONLY the EXPLICIT CONTENT used in the email.

Extract tags in this priority order ONLY IF THE EXACT KEYWORDS ARE PRESENT in the email content:
1. FEATURES mentioned in the template (e.g., 'Automated Reports', 'AI Assistant', 'Data Security')
2. OFFER if explicitly stated in the template (e.g., 'Free Trial', 'Limited Discount', 'Early Access', 'Offer') - ONLY if these exact terms appear in the template
3. MOTIVATION element if explicitly stated (e.g., 'Time Saving', 'Cost Efficiency') - ONLY if directly mentioned in the template

VERY IMPORTANT RULES:
- ONLY extract tags for elements that are EXPLICITLY STATED in the content with the EXACT KEYWORDS
- DO NOT infer, assume, or create tags for concepts that aren't directly mentioned
- DO NOT include any tag unless you can find the exact keywords in the content
- If an offer or motivation isn't explicitly stated with those exact words, DO NOT create a tag for it
- It's perfectly acceptable to have only 1 tag or even NO tags if nothing is explicitly mentioned
- DO NOT include generic tags like 'Marketing', 'Engagement', 'Problem Solution' or just the stage name
- DO NOT add any emojis to your response - we'll add those later

Email content:
{response}

Format your response as a simple NUMBERED LIST with ONLY the tags for elements that are EXPLICITLY stated in the content. For example:
1. [Feature tag - only if exact feature name is mentioned]
2. [Offer tag - only if exact offer term is present]
3. [Motivation tag - only if exact motivation is explicitly stated]
You can respond with multiple feature tags if no Offer tag and Motivation tag are found.

You can respond with "None" if no explicit tags are found."""
            
            try:
                tags_response = st.session_state.crew.delegate_work(
                    task=tag_prompt,
                    context=tag_prompt,
                    coworker="Analyst Agent"
                )
            except Exception as e:
                tags_response = "Error generating tags: " + str(e)

            if response:
                # Parse response to get template data
                template = {}
                if isinstance(response, str):
                    if "Subject:" in response:
                        try:
                            # Split by "Subject:" and then by first newline to get subject and body
                            parts = response.split("Subject:", 1)
                            if len(parts) == 2:
                                content = parts[1].strip()
                                lines = content.split('\n', 1)
                                subject = lines[0].strip()
                                body = lines[1].strip() if len(lines) > 1 else ""
                                template = {
                                    "subject": subject,
                                    "body": body
                                }
                        except Exception:
                            # Fallback if splitting fails
                            template = {
                                "subject": f"{stage} Email - {datetime.now().strftime('%Y-%m-%d')}",
                                "body": response.strip()
                            }
                    else:
                        # If no subject found, use default subject
                        template = {
                            "subject": f"{stage} Email - {datetime.now().strftime('%Y-%m-%d')}",
                            "body": response.strip()
                        }
                else:
                    # If response is already a dict, use it directly
                    template = response

                # Process tags from tags_response
                template_tags = []
                
                try:
                    # Check for 'No explicit tags found' response
                    if tags_response and 'No explicit tags found' in str(tags_response):
                        # Leave template_tags as empty list
                        pass
                    else:
                        # Convert CrewOutput to string if needed
                        tags_text = ''
                        if hasattr(tags_response, '__str__'):
                            tags_text = str(tags_response)
                        elif isinstance(tags_response, str):
                            tags_text = tags_response
                        else:
                            tags_text = f"{tags_response}"
                        
                        # Clean up the response to extract only the numbered list items
                        import re
                        
                        # Look for numbered list items (1. tag, 2. tag, etc.)
                        numbered_matches = []
                        
                        # Split by lines and process each line
                        for line in tags_text.split('\n'):
                            # Look for lines that start with a number followed by a period or parenthesis
                            match = re.match(r'^\s*\d+[\.):]\s*(.+?)\s*$', line)
                            if match:
                                tag_text = match.group(1).strip()
                                # Remove any instruction formatting like [Feature tag]
                                tag_text = re.sub(r'\[([^\]]+)\]', r'\1', tag_text)
                                # Remove explanatory text in parentheses
                                tag_text = re.sub(r'\s*\([^)]*\)\s*$', '', tag_text).strip()
                                if tag_text and len(tag_text) <= 30:
                                    numbered_matches.append(tag_text)
                        
                        if numbered_matches:
                            template_tags = [tag for tag in numbered_matches if tag]
                        
                        # If no numbered list found, try alternative methods
                        if not template_tags:
                            lines = [line.strip() for line in tags_text.split('\n') if line.strip()]
                            for line in lines:
                                # Skip lines that look like instructions or contain no explicit tags
                                if 'no explicit tags' in line.lower():
                                    continue
                                
                                # Clean up the line
                                clean_line = re.sub(r'^\d+\.\s*|^[\*\-•]\s*', '', line).strip()
                                # Only keep if it's reasonable tag length
                                if 1 <= len(clean_line) <= 30:
                                    template_tags.append(clean_line)
                except Exception as e:
                    # Just log the error and continue with empty tags
                    print(f"Error processing tags: {str(e)}")
                    
                # Filter out any remaining tags that might be instructions
                template_tags = [tag for tag in template_tags if tag 
                                and not tag.startswith('[') 
                                and not tag.endswith(']')
                                and 'tag' not in tag.lower() 
                                and 'explicit' not in tag.lower()
                                and 'found' not in tag.lower()]
                
                # Final cleanup - remove any remaining parentheses or explanations
                clean_tags = []
                for tag in template_tags:
                    # Remove parenthetical content and extra whitespace
                    clean_tag = re.sub(r'\s*\([^)]*\)\s*', ' ', tag).strip()
                    clean_tag = re.sub(r'\s+', ' ', clean_tag)  # normalize whitespace
                    if clean_tag:
                        clean_tags.append(clean_tag)
                
                template_tags = clean_tags
                
                template_data = {
                    "template_name": template_name,
                    "template": template,
                    "product_data": product_data,
                    "settings": comm_settings,
                    "channels": [comm_settings.get("utm_medium", "Email")],
                    "stage": stage,
                    "generated_at": datetime.now().isoformat(),
                    "template_number": len(templates) + 1,
                    "verified": True,
                    "tags": template_tags,
                    # Store detailed information about emoji usage
                    "has_emojis_subject": comm_settings.get("use_emojis_subject", False),
                    "has_emojis_body": comm_settings.get("use_emojis_body", False),
                    # Keep the original field for backward compatibility
                    "has_emojis": (comm_settings.get("use_emojis_subject", False) or comm_settings.get("use_emojis_body", False))
                }
                templates.append(template_data)
                total_templates += 1

                # Save after each template
                with open(stage_file, 'w') as f:
                    json.dump(templates, f, indent=4)

        # Update progress bar
        progress = (idx + 1) / len(stages)
        my_bar.progress(progress, text=f"Generated templates for stage: {stage}")

    st.success(f"Successfully generated {total_templates} templates across {len(stages)} stages!")
    return total_templates

def generate_node_email(stage, matched_product, is_followup=False, parent_email=None, generation_mode="template"):
    """Generate email for a node"""
    print(f"DEBUG: Generating email for stage={stage}, mode={generation_mode}, is_followup={is_followup}")

    # Initialize settings with defaults
    settings = {
        "tone": "professional",
        "style": "formal",
        "length": "100-150 words",
        "sender_name": "OpenEngage Team",
        "template_context": {
            "user_behavior": st.session_state.journey_user_behavior,
            "first_name": st.session_state.journey_user_name,
            "stage": stage
        }
    }

    try:
        if is_followup and parent_email:
            # Get settings from parent email or use defaults
            settings = parent_email.get('settings', settings)
            # Generate follow-up email for opened/clicked emails
            response = st.session_state.crew.generate_followup_email(matched_product.get('Product_Name'), parent_email, "opened", settings)
        else:
            # Check generation mode
            if generation_mode == "personalized":
                # Use personalized generation mode
                user_data = {
                    'first_name': st.session_state.journey_user_name,
                    'user_behaviour': st.session_state.journey_user_behavior,
                    'user_stage': stage
                }

                # Get communication settings
                from utils.file_utils import load_communication_settings
                org_url = matched_product.get('Company_URL') or matched_product.get('organization_url')
                communication_settings = load_communication_settings(organization_url=org_url)

                # Set up settings for personalized mode
                settings = {
                    "tone": communication_settings.get('style', 'professional'),
                    "style": communication_settings.get('style', 'formal'),
                    "length": communication_settings.get('length', '100-150 words'),
                    "sender_name": communication_settings.get('sender_name', 'OpenEngage Team'),
                    "template_context": {
                        "user_behavior": st.session_state.journey_user_behavior,
                        "first_name": st.session_state.journey_user_name,
                        "stage": stage
                    }
                }

                # Generate personalized email using the specialized agent
                try:
                    print(f"DEBUG: Calling personalized email generation for {stage}")
                    response = st.session_state.crew.generate_personalized_email_for_journey(
                        user_data=user_data,
                        product_data=matched_product,
                        communication_settings=communication_settings,
                        stage=stage
                    )
                    print(f"DEBUG: Personalized email response received: {type(response)}")
                except Exception as e:
                    print(f"DEBUG: Error in personalized email generation: {str(e)}")
                    st.error(f"Error in personalized email generation: {str(e)}")
                    # Fallback to a basic response
                    response = {
                        "subject": f"Personalized Email for {stage}",
                        "preheader": "A personalized message for you",
                        "content": f"Hi {user_data['first_name']},\n\nBased on your interest in {matched_product.get('Product_Name', '')}, we have a special recommendation for you.\n\nBest regards,\n{communication_settings.get('sender_name', 'OpenEngage Team')}"
                    }
            else:
                # Use template-based generation (original mode)
                from ui.templates import get_stage_template
                template = get_stage_template(stage, matched_product.get('Product_Name'))
                if template:
                    # Use the template structure from saved template
                    base_template = {
                        "template": template.get('template', {}),  # Contains subject and body
                        "product_data": template.get('product_data', matched_product),
                        "communication_settings": template.get('settings', {
                            "tone": "professional",
                            "style": "formal",
                            "length": "100-150 words",
                            "sender_name": "OpenEngage Team"
                        })
                    }

                    # Add template context
                    settings = {
                        "tone": base_template["communication_settings"].get('tone', 'professional'),
                        "style": base_template["communication_settings"].get('style', 'formal'),
                        "length": base_template["communication_settings"].get('length', '100-150 words'),
                        "sender_name": base_template["communication_settings"].get('sender_name', 'OpenEngage Team'),
                        "template_context": {
                            "base_template": base_template,
                            "user_behavior": st.session_state.journey_user_behavior,
                            "first_name": st.session_state.journey_user_name,
                            "stage": stage
                        }
                    }

                    # Generate campaign with all required fields
                    response = st.session_state.crew.generate_campaign(
                        product_data=matched_product,
                        channels=["email"],
                        settings=settings,
                        stage=stage
                    )

                    # Convert response to string if it's not already
                    if hasattr(response, '__dict__'):
                        response = str(response)
                    elif response is None:
                        return None

        # Handle the response
        if response:
            try:

                if isinstance(response, dict):
                    # Handle dictionary response from personalized email generation
                    subject = response.get("subject", f"{stage} Stage Email")
                    pre_header = response.get("preheader", response.get("pre_header", ""))
                    content = response.get("content", "")

                    # Clean up content if it contains formatting artifacts
                    if content:
                        # Remove any remaining "Pre-header:" or "Email Body:" prefixes
                        content = content.replace("Pre-header:", "").replace("Email Body:", "").strip()

                        # If content starts with a newline, remove it
                        content = content.lstrip('\n ')

                    email_data = {
                        "subject": subject,
                        "pre_header": pre_header,
                        "content": content,
                        "generated_at": datetime.now().isoformat(),
                        "stage": stage,
                        "settings": settings,
                        "cta": response.get("cta", "Click to Learn More"),
                        "cta_link": response.get("cta_link", "#")
                    }
                elif isinstance(response, str):
                    # Parse the agent's response format
                    subject = ""
                    pre_header = ""
                    body = ""

                    if "Subject:" in response:
                        # Split by "Subject:" and then by first newline to get subject and body
                        parts = response.split("Subject:", 1)
                        if len(parts) == 2:
                            content = parts[1].strip()
                            lines = content.split('\n', 1)
                            subject = lines[0].strip()
                            remaining_content = lines[1].strip() if len(lines) > 1 else ""

                            # Check for Pre-header
                            if "Pre-header:" in remaining_content:
                                pre_header_parts = remaining_content.split("Pre-header:", 1)
                                if len(pre_header_parts) == 2:
                                    pre_header_lines = pre_header_parts[1].split('\n', 1)
                                    pre_header = pre_header_lines[0].strip()
                                    body = pre_header_lines[1].strip() if len(pre_header_lines) > 1 else ""
                                else:
                                    body = remaining_content
                            else:
                                body = remaining_content
                        else:
                            # Fallback if splitting fails
                            subject = f"{stage} Stage Email"
                            pre_header = ""
                            body = response.strip()
                    else:
                        # If no subject found, use the whole response as body
                        subject = f"{stage} Stage Email"
                        pre_header = ""
                        body = response.strip()

                    email_data = {
                        "subject": subject,
                        "pre_header": pre_header,
                        "content": body,
                        "generated_at": datetime.now().isoformat(),
                        "stage": stage,
                        "settings": settings,
                        "cta": "Click to Learn More",
                        "cta_link": "#"
                    }
                else:
                    st.error(f"Unexpected response type: {type(response)}")
                    return None

                print(f"DEBUG: Successfully created email_data for {stage}")
                return email_data
            except Exception as e:
                print(f"DEBUG: Error parsing email response: {str(e)}")
                st.error(f"Error parsing email response: {str(e)}")

                # Return a fallback email structure
                fallback_content = str(response) if response else f"Generated email content for {stage}"
                fallback_email = {
                    "subject": f"Email for {stage}",
                    "pre_header": "A personalized message for you",
                    "content": fallback_content,
                    "generated_at": datetime.now().isoformat(),
                    "stage": stage,
                    "settings": settings,
                    "cta": "Click to Learn More",
                    "cta_link": "#"
                }
                print(f"DEBUG: Returning fallback email for {stage}")
                return fallback_email
        else:
            print(f"DEBUG: No response received for {stage} email generation")
            st.error(f"No response received for {stage} email generation")
            return None

    except Exception as e:
        print(f"DEBUG: Error in generate_node_email: {str(e)}")
        st.error(f"Error generating email for stage {stage}: {str(e)}")
        return None

def convert_email_to_html(email_content, product_url=None, product_name=None, company_name=None, recipient_email=None, organization_url=None):
    """Convert email content to HTML format"""
    if not email_content:
        return None

    # Get communication settings
    communication_settings = load_communication_settings(company_name)

    # Add organization URL to communication settings if available
    if organization_url:
        communication_settings['organization_url'] = organization_url
    elif product_url:
        # Extract domain from product URL as fallback
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(product_url)
            domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
            communication_settings['organization_url'] = domain
        except:
            pass

    # Extract template name from email content for CTA lookup
    template_name = None
    if 'settings' in email_content and 'template_context' in email_content['settings']:
        template_context = email_content['settings']['template_context']

        # Try to get template name from base_template
        if 'base_template' in template_context:
            base_template = template_context['base_template']

            # Check if template has a template_name field
            if 'template' in base_template and 'template_name' in base_template['template']:
                template_name = base_template['template']['template_name']
                print(f"DEBUG: Found template_name in base_template: {template_name}")

            # Add product data to communication settings
            product_data = base_template.get('product_data', {})
            if product_data:
                # Ensure the product data is included in the communication settings
                if 'template_context' not in communication_settings:
                    communication_settings['template_context'] = {}
                if 'base_template' not in communication_settings['template_context']:
                    communication_settings['template_context']['base_template'] = {}
                communication_settings['template_context']['base_template']['product_data'] = product_data

    # If no template name found, try to construct one from available data
    if not template_name and 'settings' in email_content:
        settings = email_content['settings']
        template_context = settings.get('template_context', {})

        # Try to construct template name from stage and product
        stage = template_context.get('stage', '')
        if stage and 'base_template' in template_context:
            product_data = template_context['base_template'].get('product_data', {})
            product_name_for_template = product_data.get('Product_Name', '')

            if product_name_for_template and stage:
                # Convert stage to template format (e.g., "New Visitor" -> "New_Visitor")
                stage_formatted = stage.replace(' ', '_')
                product_formatted = product_name_for_template.replace(' ', '_')

                # Try to find a matching template in the CTA JSON
                import json
                import os
                try:
                    cta_file_path = 'data/templates/template_ctas.json'
                    if os.path.exists(cta_file_path):
                        with open(cta_file_path, 'r', encoding='utf-8') as f:
                            ctas = json.load(f)

                        # Look for templates that match the pattern
                        for cta_template_name in ctas.keys():
                            if stage_formatted in cta_template_name and product_formatted in cta_template_name:
                                template_name = cta_template_name
                                print(f"DEBUG: Matched template name from CTA file: {template_name}")
                                break
                except Exception as e:
                    print(f"DEBUG: Error trying to match template name: {str(e)}")

    print(f"DEBUG: Final template_name for CTA lookup: {template_name}")

    #Convert to HTML using the email formatter
    html_content = text_to_html(
        email_content,
        product_url=product_url,
        product_name=product_name,
        communication_settings=communication_settings,
        recipient_email=recipient_email,
        template_name=template_name
    )

    # Add HTML content to email data
    email_content['html_content'] = html_content

    return email_content

def generate_tree_emails(stage, level, position, matched_product, progress_bar, start_idx, max_level=2, generation_mode="template"):
    """Generate emails for all nodes in the tree"""
    try:
        # Calculate node number (1-7) from level and position
        node_number = 2**level + position
        node_id = f'node_{node_number}'

        # Generate email for current node
        if level == 0:  # Root node (node 1)
            email = generate_node_email(stage, matched_product, generation_mode=generation_mode)
            if email:
                email['node_type'] = 'initial'
                # Convert to HTML
                # Use user email from session state if available
                recipient_email = st.session_state.get('user_email') if hasattr(st.session_state, 'user_email') else None
                email = convert_email_to_html(
                    email,
                    matched_product.get('Product_URL'),
                    matched_product.get('Product_Name'),
                    matched_product.get('Company_Name'),
                    recipient_email,
                    matched_product.get('Company_URL') or matched_product.get('organization_url')
                )
                save_node_email(node_id, email)
                print(f"DEBUG: Successfully saved email for {node_id}")
            else:
                print(f"DEBUG: Failed to generate email for {node_id} in {generation_mode} mode")
        else:
            # Calculate parent node number
            parent_number = node_number // 2
            parent_id = f'node_{parent_number}'
            is_right = node_number % 2 == 1

            if is_right:  # Next stage node
                email = generate_node_email(stage, matched_product, generation_mode=generation_mode)
                if email:
                    email['node_type'] = 'next_stage'
                    # Convert to HTML
                    email = convert_email_to_html(
                        email,
                        matched_product.get('Product_URL'),
                        matched_product.get('Product_Name'),
                        matched_product.get('Company_Name'),
                        None,  # recipient_email
                        matched_product.get('Company_URL') or matched_product.get('organization_url')
                    )
                    save_node_email(node_id, email)
            else:  # Same stage node (follow-up)
                parent_email = load_node_email(parent_id)
                if parent_email:
                    email = generate_node_email(stage, matched_product, True, parent_email, generation_mode=generation_mode)
                    if email:
                        email['node_type'] = 'follow_up'
                        # Convert to HTML
                        email = convert_email_to_html(
                            email,
                            matched_product.get('Product_URL'),
                            matched_product.get('Product_Name'),
                            matched_product.get('Company_Name'),
                            None,  # recipient_email
                            matched_product.get('Company_URL') or matched_product.get('organization_url')
                        )
                        save_node_email(node_id, email)

        # Update progress
        total_nodes = 7  # Total nodes in a binary tree of depth 2
        progress = min(1.0, node_number / total_nodes)
        progress_bar.progress(progress)

        # Generate emails for children if not at max level
        if level < max_level:
            left_child_stage = stage  # Left child stays at same stage
            right_child_stage = st.session_state.journey_stages[start_idx + level + 1] if start_idx + level + 1 < len(st.session_state.journey_stages) else None

            # Generate left child (follow-up path)
            generate_tree_emails(left_child_stage, level + 1, position * 2, matched_product, progress_bar, start_idx, max_level, generation_mode)

            # Generate right child (next stage path)
            if right_child_stage:
                generate_tree_emails(right_child_stage, level + 1, position * 2 + 1, matched_product, progress_bar, start_idx, max_level, generation_mode)

    except Exception as e:
        st.error(f"Error generating email for node {node_id}: {str(e)}")
        return None
