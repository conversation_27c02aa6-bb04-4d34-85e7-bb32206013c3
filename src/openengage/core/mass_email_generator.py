"""
Mass email generation functionality for OpenEngage.
"""
import os
import json
import pandas as pd
import numpy as np
import re
import yaml
from pathlib import Path
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Callable
import logging
import streamlit as st
from core.product_selector import select_product_for_user
from core.email_formatter import text_to_html

def process_mass_email_data(data_df, progress_callback=None, use_batch_api=True):
    """
    Process mass email data for generating personalized campaigns.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        progress_callback (callable, optional): Function to report progress (current, total, message)
        use_batch_api (bool): Whether to use OpenAI's Batch API for faster processing

    Returns:
        pd.DataFrame: DataFrame with generated email content
    
    Notes:
        This function also saves user data to Sample Data For Mass Generation/user_popup_data.csv 
        with email, behavior data, target product, and user stage for each processed user.
    """

    def process_single_user(user_row, products):
        """Process a single user's data and generate email content"""
        try:
            # Check if user has behavior data
            has_behavior_data = ("Behaviour data not found" not in user_row['user_behaviour']) or ("last opened an email" in user_row['user_behaviour'])

            # If no behavior data, randomly assign a product
            if not has_behavior_data:
                matched_product, similarity = select_product_for_user(user_row, products)
                template_file = f'data/templates/{user_row["user_stage"].lower().replace(" ", "_")}.json'
                if os.path.exists(template_file):
                    with open(template_file, 'r') as f:
                        templates = json.load(f)
                        # Only use verified templates and match product name
                        matching_templates = [t for t in templates if t.get('product_data', {}).get('Product_Name') == matched_product.get('Product_Name') and t.get('verified', False) is True]
                        if matching_templates:
                            template = matching_templates[-1]
                            if template:
                                return {
                                    'Subject': template.get('template', {}).get('subject', ''),
                                    'Mail_Content': template.get('template', {}).get('body', ''),
                                    'Preheader': template.get('template', {}).get('preheader', ''),
                                    'Matched_Product': matched_product.get('Product_Name', ''),
                                    'Similarity_Score': similarity,
                                    'Template_Name': f"Template_{template.get('template_name', '')}"
                                }
            else:
                matched_product, similarity = select_product_for_user(user_row, products)
                template_file = f'data/templates/{user_row["user_stage"].lower().replace(" ", "_")}.json'
                if os.path.exists(template_file):
                    with open(template_file, 'r') as f:
                        templates = json.load(f)
                        # Only use verified templates and match product name
                        matching_templates = [t for t in templates if t.get('product_data', {}).get('Product_Name') == matched_product.get('Product_Name') and t.get('verified', False) is True]
                        if matching_templates:
                            template = matching_templates[-1]
                            if template:
                                base_template = {
                                    "template": template.get('template', {}),
                                    "product_data": template.get('product_data', matched_product),
                                    "communication_settings": template.get('settings', {
                                        "tone": "professional",
                                        "style": "formal",
                                        "length": "100-150 words",
                                        "sender_name": "OpenEngage Team",
                                        "brand_personality": "Professional, Helpful, Trustworthy",
                                        "tone_of_voice": "Professional, Informative"
                                    })
                                }

                            settings = {
                                "tone": base_template.get('communication_settings', {}).get('tone', 'professional'),
                                "style": base_template.get('communication_settings', {}).get('style', 'formal'),
                                "length": base_template.get('communication_settings', {}).get('length', '100-150 words'),
                                "sender_name": base_template.get('communication_settings', {}).get('sender_name', 'OpenEngage Team'),
                                "template_context": {
                                    "base_template": base_template,
                                    "user_behavior": user_row['user_behaviour'] if has_behavior_data else f"Interested in {matched_product.get('Product_Name')}",
                                    "first_name": user_row['first_name'],
                                    "stage": user_row['user_stage']
                                }
                            }
                            try:
                                response = st.session_state.crew.generate_campaign(
                                    product_data=matched_product,
                                    channels=["email"],
                                    settings=settings,
                                    stage=user_row['user_stage']
                                )
                            except Exception as e:
                                response = None
                            if isinstance(response, dict):
                                return {
                                    'Subject': response.get('subject', ''),
                                    'Mail_Content': response.get('content', ''),
                                    'Preheader': response.get('preheader', ''),
                                    'Matched_Product': matched_product.get('Product_Name', ''),
                                    'Similarity_Score': similarity,
                                    'Template_Name': base_template.get('template', {}).get('template_name', '')
                                }
                            elif isinstance(response, str):
                                if "Subject:" in response:
                                    parts = response.split("Subject:", 1)
                                    if len(parts) == 2:
                                        content = parts[1].strip()
                                        lines = content.split('\n', 1)
                                        mail_content = lines[1].strip() if len(lines) > 1 else ""
                                        preheader = ""
                                        if "Pre-header:" in mail_content:
                                            parts = mail_content.split("Pre-header:", 1)
                                            if len(parts) == 2:
                                                preheader_parts = parts[1].strip().split('\n', 1)
                                                preheader = preheader_parts[0].strip()
                                                mail_content = preheader_parts[1].strip() if len(preheader_parts) > 1 else parts[0]
                                        return {
                                            'Subject': lines[0].strip(),
                                            'Mail_Content': mail_content,
                                            'Preheader': preheader,
                                            'Matched_Product': matched_product.get('Product_Name', ''),
                                            'Similarity_Score': similarity,
                                            'Template_Name': base_template.get('template', {}).get('template_name', '')
                                        }
                                return {
                                    'Subject': 'Personalized Recommendation',
                                    'Mail_Content': response,
                                    'Preheader': '',
                                    'Matched_Product': matched_product.get('Product_Name', ''),
                                    'Similarity_Score': similarity,
                                    'Template_Name': 'AI_Generated'
                                }

            return {
                'Subject': '',
                'Mail_Content': '',
                'Preheader': '',
                'Matched_Product': matched_product.get('Product_Name', '') if 'matched_product' in locals() and matched_product else '',
                'Similarity_Score': similarity if 'similarity' in locals() else 0.0,
                'Template_Name': 'None'
            }
        except Exception as e:
            st.error(f"Error processing user {user_row['first_name']}: {str(e)}")
            return {
                'Subject': '',
                'Mail_Content': '',
                'Preheader': '',
                'Matched_Product': '',
                'Similarity_Score': 0.0,
                'Template_Name': 'Error'
            }

    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Load all products
    products = []
    try:
        with open('data/product_details.json', 'r') as f:
            products = json.load(f)
            if not isinstance(products, list):
                products = [products]
            if org_filter_enabled and org_url:
                products = [p for p in products if p.get("Company_URL", "") == org_url]
    except Exception as e:
        st.error("Error loading product details")
        return data_df

    if not products:
        if org_filter_enabled and org_url:
            st.error(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
        else:
            st.error("No products found. Please add products first.")
        return data_df

    # Use batch API if enabled
    if use_batch_api:
        try:
            from core.batch_email_generator import generate_emails_batch
            return generate_emails_batch(data_df, products, progress_callback)
        except Exception as e:
            use_batch_api = False

    # If batch API is disabled or failed, use sequential processing
    if not use_batch_api:
        total_users = len(data_df)
        for idx, row in data_df.iterrows():
            if progress_callback:
                progress_callback(idx, total_users, f"Processing user: {row['first_name']}")
            result = process_single_user(row, products)
            data_df.at[idx, 'Subject'] = result['Subject']
            data_df.at[idx, 'Mail_Content'] = result['Mail_Content']
            data_df.at[idx, 'Preheader'] = result['Preheader']
            data_df.at[idx, 'Matched_Product'] = result['Matched_Product']
            data_df.at[idx, 'Similarity_Score'] = round(float(result['Similarity_Score']) * 100, 2)
            data_df.at[idx, 'Template_Name'] = result.get('Template_Name', 'None')
            if result['Subject'] and result['Mail_Content']:
                if progress_callback:
                    progress_callback(idx, total_users, f"Generating HTML for: {row['first_name']}")
                has_behavior_data = ("Behaviour data not found" not in row['user_behaviour']) or ("last opened an email" in row['user_behaviour'])
                if has_behavior_data:
                    matched_product, _ = select_product_for_user(row, products)
                else:
                    matched_product = next((p for p in products if p.get('Product_Name') == result['Matched_Product']), products[0])
                email_content = {
                    'subject': result['Subject'],
                    'content': result['Mail_Content'],
                    'preheader': result.get('Preheader', '')
                }
                # Load communication settings for this organization
                from utils.file_utils import load_communication_settings
                org_url = matched_product.get('Company_URL', '') or matched_product.get('organization_url', '')
                comm_settings = load_communication_settings(organization_url=org_url)

                settings = {
                    "sender_name": matched_product.get('Company_Name', 'OpenEngage Team'),
                    "style": comm_settings.get("style", "friendly"),
                    "length": comm_settings.get("length", "100-150 words"),
                    "utm_source": "mass_email",
                    "utm_medium": "email",
                    "utm_campaign": "personalized_campaign",
                    "utm_content": row['user_stage'].lower().replace(" ", "_"),
                    "brand_personality": comm_settings.get("brand_personality", "Professional, Helpful, Trustworthy"),
                    "tone_of_voice": comm_settings.get("tone_of_voice", "Professional, Informative"),
                    "organization_url": org_url,  # Add organization URL directly to settings
                    "template_context": {
                        "base_template": {
                            "product_data": matched_product
                        }
                    }
                }
                # Get the correct row for this specific user (idx)
                user_row = data_df.loc[idx]

                # Convert to HTML with the correct user's data
                html_content = text_to_html(
                    email_content,
                    product_url=matched_product.get('Product_URL'),
                    product_name=matched_product.get('Product_Name'),
                    communication_settings=settings,
                    recipient_email=user_row.get('user_email') if hasattr(user_row, 'get') else user_row['user_email'],
                    recipient_first_name=user_row.get('first_name') if hasattr(user_row, 'get') else user_row['first_name']
                )

                # Add HTML content to the DataFrame
                data_df.at[idx, 'HTML_Content'] = html_content
        if progress_callback:
            progress_callback(total_users, total_users, "Saving HTML emails")
        os.makedirs("data/html_emails", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        for idx, row in data_df[:5].iterrows():
            if 'HTML_Content' in row and row['HTML_Content'] and isinstance(row['HTML_Content'], str):
                try:
                    email_filename = f"data/html_emails/mass_email_{timestamp}_{idx}.html"
                    with open(email_filename, 'w') as f:
                        f.write(row['HTML_Content'])
                except Exception as e:
                    st.error(f"Error saving HTML file for user {row.get('first_name', idx)}: {str(e)}")
            elif 'HTML_Content' in row and row['HTML_Content'] is not None and not isinstance(row['HTML_Content'], str):
                try:
                    data_df.at[idx, 'HTML_Content'] = str(row['HTML_Content'])
                except:
                    data_df.at[idx, 'HTML_Content'] = ""
                    
    # Save popup data to user_popup_data.csv with personalized popup content
    try:
        # Create popup data dictionary with additional fields for popup content
        popup_data = {
            'user_email': [],
            'behaviour_data': [],
            'target_product': [],
            'user_stage': [],
            'popup_title': [],
            'popup_text': [],
            'button_text': [],
            'redirect_url': [],
            'template_name': []  # Add Template_Name field
        }
        
        # Base URLs for products
        product_urls = {
            'Agentic AI Pioneer Program': 'https://www.analyticsvidhya.com/agentic-ai',
            'Certified AI/ML BlackBelt Plus Program': 'https://www.analyticsvidhya.com/blackbelt',
            'GenAI Pinnacle Plus Program': 'https://www.analyticsvidhya.com/pinnacleplus'
        }
        
        if progress_callback:
            progress_callback(total_users, total_users, "Generating personalized popup content")
        
        # Collect data from all processed users and generate popup content
        for idx, row in data_df.iterrows():
            user_email = row['user_email']
            behaviour_data = row.get('user_behaviour', '')
            target_product = row.get('Matched_Product', '')
            user_stage = row.get('user_stage', '')
            first_name = row.get('first_name', user_email.split('@')[0])
            
            # Add base data
            popup_data['user_email'].append(user_email)
            popup_data['behaviour_data'].append(behaviour_data)
            popup_data['target_product'].append(target_product)
            popup_data['user_stage'].append(user_stage)
            popup_data['template_name'].append(row.get('Template_Name', 'None'))
            
            # Set product-specific redirect URL
            redirect_url = product_urls.get(target_product, 'https://www.analyticsvidhya.com')
            # Add UTM parameters
            redirect_url += f'?utm_source=popup&utm_medium=website&utm_campaign={user_stage.lower().replace(" ", "_")}&utm_content={target_product.lower().replace(" ", "_")}'
            
            # Generate personalized popup content based on user data
            try:
                # Check if behavior data is not found
                behaviour_not_found = 'Behaviour data not found' in behaviour_data
                
                # Load prompt template from prompts.yml
                prompts_path = Path(__file__).parent.parent.parent.parent / 'config' / 'prompts.yml'
                try:
                    with open(prompts_path, 'r') as f:
                        prompts = yaml.safe_load(f)
                        popup_template = prompts.get('popup_generation', {}).get('template', '')
                except Exception as e:
                    logging.error(f"Error loading prompts.yml: {str(e)}")
                    popup_template = ""
                
                # If template couldn't be loaded, use a default template
                if not popup_template:
                    popup_template = """Generate personalized popup content based on user data.
                    Return a JSON with popup_title, popup_text, and button_text fields."""
                
                # Use LLM or existing templates for generation
                if 'st' in globals() and hasattr(st.session_state, 'crew'):
                    # Format the template with user data
                    popup_prompt = popup_template.format(
                        first_name=first_name,
                        user_behavior=behaviour_data,
                        target_product=target_product,
                        user_stage=user_stage
                    )
                    
                    try:
                        # Call LLM for content generation using the formatted prompt
                        response = st.session_state.crew.generate_popup_content(popup_prompt)
                        
                        if isinstance(response, dict):
                            popup_title = response.get('popup_title', f'Special offer on {target_product}')
                            popup_text = response.get('popup_text', '') if not behaviour_not_found else ''
                            button_text = response.get('button_text', 'Learn More')
                        else:
                            # Fallback content
                            popup_title = f'Exclusive offer on {target_product}'
                            popup_text = '' if behaviour_not_found else f'Hi {first_name}, based on your interests, we\'ve selected {target_product} just for you. Take advantage of our special offer today!'
                            button_text = 'See Details'
                    except Exception as e:
                        # Fallback content on error
                        popup_title = f'Discover {target_product}'
                        popup_text = '' if behaviour_not_found else f'Hi {first_name}, we think you\'ll love our {target_product} based on your recent activity.'
                        button_text = 'Learn More'
                else:
                    # Generate based on user stage and behavior data
                    popup_title = f'Discover {target_product}'
                    button_text = 'Learn More'
                    
                    # Leave popup text blank if behavior data not found
                    if behaviour_not_found:
                        popup_text = ''
                    else:
                        if user_stage == 'Product Lead Generated':
                            popup_title = f'Complete Your {target_product} Journey'
                            popup_text = f'Hi {first_name}, you\'re just a few steps away from unlocking the full potential of {target_product}. Complete your purchase today!'
                            button_text = 'Continue'
                        elif user_stage == 'Product Purchased':
                            popup_title = f'Enhance Your {target_product} Experience'
                            popup_text = f'Hi {first_name}, thank you for purchasing {target_product}. Discover additional resources to maximize your learning journey!'
                            button_text = 'Explore Resources'
                        elif user_stage == 'Product Page Viewed':
                            popup_title = f'Special Offer on {target_product}'
                            popup_text = f'Hi {first_name}, we noticed your interest in {target_product}. Take advantage of our limited-time offer today!'
                            button_text = 'Get Started'
                        else:  # New Visitor
                            popup_text = f'Hi {first_name}, based on your interests, we recommend our {target_product}. Start your journey today!'
            except Exception as content_error:
                # Default content on error
                popup_title = f'Discover {target_product}'
                # Check if behavior data not found
                popup_text = '' if 'Behaviour data not found' in behaviour_data else f'Hi {first_name}, we have a special offer just for you on our {target_product}.'
                button_text = 'Learn More'
                if 'st' in globals():
                    st.error(f"Error generating popup content for {user_email}: {str(content_error)}")
            
            # Add generated content to the data
            popup_data['popup_title'].append(popup_title)
            popup_data['popup_text'].append(popup_text)
            popup_data['button_text'].append(button_text)
            popup_data['redirect_url'].append(redirect_url)
        
        # Create DataFrame and save to CSV
        popup_df = pd.DataFrame(popup_data)
        popup_path = os.path.join(os.getcwd(), 'Sample Data For Mass Generation/user_popup_data.csv')
        popup_df.to_csv(popup_path, index=False)
        
        if progress_callback:
            progress_callback(total_users, total_users, "Saved personalized popup data")
    except Exception as e:
        if 'st' in globals():
            st.error(f"Error saving user popup data: {str(e)}")

    return data_df
