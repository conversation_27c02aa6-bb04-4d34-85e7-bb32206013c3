"""
This module contains tools for delegating work and analyzing products.

Classes:
- DelegateWorkTool: A tool for delegating tasks to coworkers.
- ProductAnalyzerTool: A tool for analyzing product details from scraped content.
- WebScrapingTool: A tool for scraping web content.
- AgentFactory: A factory class for creating agents with specific roles.
"""

from crewai import Agent
from crewai.tools import BaseTool
from langchain.tools import Tool
from typing import List, Dict, Optional, Any
from dotenv import load_dotenv
import os
import json
from openai import OpenAI
from langchain_openai import ChatOpenAI
from bs4 import BeautifulSoup
from pydantic import BaseModel, Field, ValidationError
import re

# Load environment variables
load_dotenv()

class ProductAnalyzerOutput(BaseModel):
    """
    Model for the output of product analysis.
    """
    Product_Name: str = Field(description="Name of the product")
    Company_Name: str = Field(description="Name of the company")
    Type_of_Product: str = Field(description="Type/category of the product")
    Product_Features: List[str] = Field(description="List of key product features")
    Product_Summary: str = Field(description="Detailed summary of the product")

class OrganizationAnalyzerOutput(BaseModel):
    """
    Model for the output of organization analysis.
    """
    Domain: str = Field(description="Business Domain of the organization")
    WhatWeDo: str = Field(description="Organization's main purpose/activities")
    AboutUs: str = Field(description="About the organization")
    Class: str = Field(description="Business class category (EdTech, E-commerce, etc.)")


class DelegateWorkTool(BaseTool):
    """
    Tool for delegating work to coworkers.
    
    Attributes:
    name (str): The name of the tool.
    description (str): The description of the tool.
    """
    
    name: str = "delegate_work"
    description: str = "Delegates work to coworkers"
    
    def _execute(self, task: str) -> str:
        """
        Execute the delegation task.
        
        Args:
        task (str): The task to delegate.
        
        Returns:
        str: Confirmation of task delegation.
        """
        return f"Task delegated: {task}"
        
    def _run(self, task: str) -> str:
        """Run the tool with the given task."""
        return self._execute(task)

class ProductAnalyzerTool(BaseTool):
    """Tool for analyzing product details from scraped content."""
    
    name: str = "Product Analyzer"
    description: str = "Analyzes product details from scraped content"
    
    def _execute(self, content: str) -> Dict[str, Any]:
        """Execute the product analysis on the given content."""
        try:
            client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            
            # Send the scraped content to the GPT model
            response = client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a product analyzer. Extract the following details from the provided content:
                        - Product Name
                        - Company Name
                        - Type of Product
                        - Key Features (as a list)
                        - Detailed Product Summary in 200 words.
                        
                        Return the information in a JSON format with these exact keys:
                        {
                            "Product_Name": "...",
                            "Company_Name": "...",
                            "Type_of_Product": "...",
                            "Product_Features": ["...", "..."],
                            "Product_Summary": "..."
                        }
                        
                        If any field is not found, use reasonable defaults based on available information.
                        Never return null or empty values."""
                    },
                    {
                        "role": "user",
                        "content": f"Extract structured product details from the following content:\n{content}"
                    }
                ],
                temperature=0.9
            )
            
            # Parse the response and ensure it's valid JSON
            try:
                json_str = response.choices[0].message.content.strip()
                # Find JSON content between curly braces
                json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                json_output = json.loads(json_str)
                
                # Ensure all required fields are present with defaults if needed
                default_output = {
                    "Product_Name": "Unknown Product",
                    "Company_Name": "Unknown Company",
                    "Type_of_Product": "Unknown Type",
                    "Product_Features": ["No features available"],
                    "Product_Summary": "No summary available"
                }
                
                for key in default_output:
                    if key not in json_output or not json_output[key]:
                        json_output[key] = default_output[key]
                
                # Ensure Product_Features is a list
                if not isinstance(json_output["Product_Features"], list):
                    if isinstance(json_output["Product_Features"], str):
                        json_output["Product_Features"] = [json_output["Product_Features"]]
                    else:
                        json_output["Product_Features"] = ["No features available"]
                
                return json_output
                
            except (json.JSONDecodeError, AttributeError) as e:
                return {
                    "Product_Name": "Error Processing Product",
                    "Company_Name": "Unknown Company",
                    "Type_of_Product": "Unknown Type",
                    "Product_Features": ["Error processing features"],
                    "Product_Summary": f"Error processing product details: {str(e)}"
                }
            
        except Exception as e:
            return {
                "Product_Name": "Error Processing Product",
                "Company_Name": "Error",
                "Type_of_Product": "Error",
                "Product_Features": ["Error occurred"],
                "Product_Summary": f"Failed to analyze product: {str(e)}"
            }

    def _run(self, content: str) -> str:
        """Run the tool with the given content."""
        result = self._execute(content)
        return json.dumps(result, indent=2)

class OrganizationAnalyzerTool(BaseTool):
    """Tool for analyzing organization details from website."""
    
    name: str = "Organization Analyzer"
    description: str = "Analyzes organization details from website content"
    
    def _execute(self, content: str) -> Dict[str, Any]:
        """Execute the organization analysis on the given content."""
        try:
            client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            
            response = client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are an organization analyzer. Extract the following details from the provided content:
                        - Business Domain of the organization (Eg:- Edtech, Healthcare etc.)
                        - What We Do
                        - About Us
                        - Class (one of the following: EdTech, E-commerce, Insurance, Banking, Quick-Commerce, TravelTech, Creator Economy Platform). Food is considered in Quick Commerce and Example of Creator Economy Platform is Substack, Patreon etc.
                        
                        Return the information in a JSON format with these exact keys:
                        {
                            "Domain": "...",
                            "WhatWeDo": "...",
                            "AboutUs": "...",
                            "Class": "..."
                        }
                        
                        If any field is not found, use reasonable defaults based on available information.
                        Never return null or empty values."""
                    },
                    {
                        "role": "user",
                        "content": f"Extract structured organization details from the following content:\n{content}"
                    }
                ],
                temperature=0.9
            )
            
            try:
                json_str = response.choices[0].message.content.strip()
                json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                json_output = json.loads(json_str)
                
                default_output = {
                    "Domain": "Unknown Domain",
                    "WhatWeDo": "Information not available",
                    "AboutUs": "Information not available",
                    "Class": "Unknown Class"
                }
                
                for key in default_output:
                    if key not in json_output or not json_output[key]:
                        json_output[key] = default_output[key]
                
                return json_output
                
            except (json.JSONDecodeError, AttributeError) as e:
                return {
                    "Domain": "Error Processing Domain",
                    "WhatWeDo": "Error processing information",
                    "AboutUs": f"Error processing details: {str(e)}",
                    "Class": "Unknown Class"
                }
            
        except Exception as e:
            return {
                "Domain": "Error",
                "WhatWeDo": "Error occurred",
                "AboutUs": f"Failed to analyze organization: {str(e)}",
                "Class": "Unknown Class"
            }

    def _run(self, content: str) -> str:
        """Run the tool with the given content."""
        result = self._execute(content)
        return json.dumps(result, indent=2)


class WebScrapingTool(BaseTool):
    """Tool for scraping web content."""
    
    name: str = "Web Scraper"
    description: str = "Scrapes content from a given URL"
    
    def _execute(self, url: str) -> str:
        """Execute the web scraping on the given URL."""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
                
            # Get text content
            text = soup.get_text(separator='\n', strip=True)
            
            # Clean up text
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            content = '\n'.join(lines)
            
            return content
            
        except Exception as e:
            return f"Error scraping URL: {str(e)}"
            
    def _run(self, url: str) -> str:
        """Run the tool with the given URL."""
        return self._execute(url)

class AgentFactory:
    """Factory class for creating agents with specific roles"""
    
    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        if not self.openai_api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        
    def create_master_agent(self) -> Agent:
        """Create the master chat agent."""
        delegate_tool = DelegateWorkTool()
        product_analyzer = ProductAnalyzerTool()
        organization_analyzer = OrganizationAnalyzerTool()
        
        return Agent(
            role="Master Chat Agent",
            goal="Coordinate product analysis and marketing campaign generation",
            backstory="""You are the master chat agent for OpenEngage, a marketing automation system. 
            Your role is to coordinate product analysis and delegate tasks to specialized agents.""",
            allow_delegation=True,
            verbose=True,
            memory=True,
            tools=[delegate_tool, product_analyzer, organization_analyzer],
            llm=ChatOpenAI(
                api_key=self.openai_api_key,
                model="gpt-4o-mini-2024-07-18",
                temperature=0.7
            )
        )
        
    def create_campaign_agent(self) -> Agent:
        """Create the campaign generation agent."""
        return Agent(
            role="Campaign Generation Agent",
            goal="Generate effective marketing campaigns",
            backstory="""You are a marketing specialist who creates compelling email and WhatsApp campaigns.
            You analyze product details and create targeted marketing content.""",
            verbose=True,
            memory=True,
            llm=ChatOpenAI(
                api_key=self.openai_api_key,
                model="ft:gpt-4o-mini-2024-07-18:analytics-vidhya:oev1mailgen:B7xdiKXj",
                temperature=0.7
            )
        )
        
    def create_analyst_agent(self) -> Agent:
        """Create the analyst agent."""
        return Agent(
            role="Analyst Agent",
            goal="Analyze campaign performance metrics",
            backstory="""You are a data analyst who specializes in analyzing marketing campaign performance.
            You track metrics like open rates, click rates, and conversion rates.""",
            verbose=True,
            memory=True,
            llm=ChatOpenAI(
                api_key=self.openai_api_key,
                model="gpt-4o-mini-2024-07-18",
                temperature=0.3
            )
        )

    def create_personalized_email_agent(self) -> Agent:
        """Create the personalized email generation agent for end-to-end generation."""
        return Agent(
            role="Personalized Email Generation Agent",
            goal="Generate fully personalized emails without using templates",
            backstory="""You are an expert email marketer who creates highly personalized emails
            based on user behavior and product information. Your emails are tailored to each
            individual recipient's needs and interests. You specialize in creating emails that
            feel like they were written specifically for each user, incorporating their specific
            behavior patterns and addressing their unique needs. You follow the HOOK-TRUST-PITCH-MOTIVATION
            framework to create compelling, personalized content.""",
            verbose=True,
            memory=True,
            llm=ChatOpenAI(
                api_key=self.openai_api_key,
                model="ft:gpt-4o-mini-2024-07-18:analytics-vidhya:personalizedmailgeneratorendtoend:BfREaoS1",
                temperature=0.7
            )
        )
