import os
import sqlite3
import json
import yaml
import traceback
from dotenv import load_dotenv
from typing import Annotated, Literal, Any, Dict, List, Optional, Union

#!/usr/bin/env python3
"""
CSV Performance Data Processor

This script processes the all_performance_202505.csv file and transforms it into a format 
compatible with the email marketing insights agent database schema.

Output columns:
- send_date: Date when the email was sent
- template_id: Identifier for the email template used
- subject_line: Subject line of the email
- pre_header_text: Pre-header text of the email
- email_body: Body content of the email
- emails_sent: Number of emails sent in campaign
- emails_unsubscribed: Number of unsubscribes from the campaign
- emails_clicked: Number of clicks on the campaign
- emails_opened: Number of opens for the campaign
- sender_info: Information about the sender

The processed data is saved in the data/mail_performance/combined directory.
"""

import os
import csv
import pandas as pd
from datetime import datetime
from collections import defaultdict

# Load environment variables
load_dotenv()

# Load prompts and SQL queries from config files
def load_config():
    """Load prompts and SQL queries from the config files"""
    config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config')
    
    # Load prompts
    prompts_path = os.path.join(config_dir, 'prompts.yml')
    try:
        with open(prompts_path, 'r', encoding='utf-8') as file:
            prompts_config = yaml.safe_load(file)
            prompts = prompts_config['sql_agent']
    except FileNotFoundError:
        print(f"Prompts config file not found at: {prompts_path}")
        raise
    except KeyError:
        print("sql_agent section not found in prompts config")
        raise
    
    # Load SQL queries
    queries_path = os.path.join(config_dir, 'sql_queries.yml')
    try:
        with open(queries_path, 'r', encoding='utf-8') as file:
            queries_config = yaml.safe_load(file)
    except FileNotFoundError:
        print(f"SQL queries config file not found at: {queries_path}")
        raise
    
    return prompts, queries_config

# Load configuration before any usage
prompts, sql_queries = load_config()
print("Prompts and SQL queries loaded from config files.")
DB_NAME = "email_marketing.db"
connection = sqlite3.connect(DB_NAME)
cursor = connection.cursor()
print(f"Connected to new database: {DB_NAME}")

table_creation_query = sql_queries['database_setup']['create_table']
cursor.execute(table_creation_query)



# Define paths
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
INPUT_FILE = os.path.join(BASE_DIR,'data', 'mail_performance', 'combined', 'all_performance_202505.csv')
OUTPUT_DIR = os.path.join(BASE_DIR, 'data', 'mail_performance', 'combined')
OUTPUT_FILE = os.path.join(OUTPUT_DIR, 'processed_performance_data.csv')

def ensure_directory_exists(directory_path):
    """Ensure the specified directory exists, creating it if necessary."""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"Created directory: {directory_path}")

def process_csv_data():
    """
    Process the all_performance_202505.csv file and aggregate the data by campaign.
    
    Returns:
        A list of dictionaries containing the processed data.
    """
    try:
        # Read the CSV file using pandas
        print(f"Reading data from {INPUT_FILE}...")
        df = pd.read_csv(INPUT_FILE)
        
        # Group by Campaign_ID to aggregate metrics
        campaign_groups = df.groupby(['campaign_date', 'Template_Name', 'Subject', 'Preheader'])
        
        processed_data = []
        
        for group_key, group_data in campaign_groups:
            campaign_date, template_name, subject, preheader = group_key
            
            # Extract a sample mail content (taking the first non-null value)
            mail_content = next((content for content in group_data['Mail_Content'] if pd.notna(content)), '')
            
            # Count metrics
            emails_sent = len(group_data)
            emails_opened = group_data['Open_Time'].notna().sum()
            emails_clicked = group_data['Click_Time'].notna().sum()
            emails_unsubscribed = group_data['Unsubscribe_Time'].notna().sum()
            
            # Default sender info if not available in the data
            sender_info = "OpenEngage Marketing <<EMAIL>>"
            
            # Format the date
            try:
                # Parse the date and format it as YYYY-MM-DD
                send_date = datetime.strptime(campaign_date, '%Y-%m-%d').strftime('%Y-%m-%d')
            except (ValueError, TypeError):
                # If date parsing fails, use the original value
                send_date = campaign_date if pd.notna(campaign_date) else "Unknown"
            
            # Create a record with the required columns
            record = {
                'send_date': send_date,
                'template_id': template_name if pd.notna(template_name) else f"TEMPLATE_{campaign_id}",
                'subject_line': subject if pd.notna(subject) else "No Subject",
                'pre_header_text': preheader if pd.notna(preheader) else "",
                'email_body': mail_content,
                'emails_sent': emails_sent,
                'emails_unsubscribed': emails_unsubscribed,
                'emails_clicked': emails_clicked,
                'emails_opened': emails_opened,
                'sender_info': sender_info
            }
            
            processed_data.append(record)
            
        print(f"Processed {len(processed_data)} unique campaigns.")
        return processed_data
        
    except Exception as e:
        print(f"Error processing CSV file: {str(e)}")
        return []

def save_processed_data(processed_data):
    """Save the processed data to a CSV file."""
    try:
        ensure_directory_exists(OUTPUT_DIR)
        
        if not processed_data:
            print("No data to save.")
            return False
            
        # Write to CSV
        with open(OUTPUT_FILE, 'w', newline='') as csvfile:
            fieldnames = [
                'send_date', 'template_id', 'subject_line', 'pre_header_text',
                'email_body', 'emails_sent', 'emails_unsubscribed', 
                'emails_clicked', 'emails_opened', 'sender_info'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in processed_data:
                writer.writerow(record)
                
        print(f"Processed data saved to {OUTPUT_FILE}")
        return True
        
    except Exception as e:
        print(f"Error saving processed data: {str(e)}")
        return False
def insert_data(processed_data):
    """Insert processed data into the SQLite database.
    
    Converts dictionary records to tuples with values in the exact order expected by the SQL query.
    """
    # --- Phase 3: Insert Sample Data ---
    insert_query = sql_queries['database_setup']['insert_sample_data']
    
    # Define the order of columns as per the SQL query
    column_order = [
        'send_date', 'template_id', 'subject_line', 'pre_header_text', 'email_body',
        'emails_sent', 'emails_unsubscribed', 'emails_clicked', 'emails_opened', 'sender_info'
    ]
    
    # Convert dictionaries to tuples in the correct order
    tuples_to_insert = []
    for record in processed_data:
        # Create a tuple with values in the order defined by column_order
        row_tuple = tuple(record[col] for col in column_order)
        tuples_to_insert.append(row_tuple)
    
    if tuples_to_insert:
        cursor.executemany(insert_query, tuples_to_insert)
        connection.commit()
        print(f"Inserted {len(tuples_to_insert)} sample rows into 'campaign_data'.")
    else:
        print("No data to insert into the database.")

def main():
    """Main function to process the data and save the results."""
    print("Starting performance data processing...")
    processed_data = process_csv_data()
    success = save_processed_data(processed_data)
    insert_data(processed_data)
    if success:
        print("Performance data processing completed successfully.")
    else:
        print("Performance data processing completed with errors.")

if __name__ == "__main__":
    main()
    connection.close()
