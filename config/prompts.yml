campaign_generation:
  template: |
    Generate a personalized email campaign for the {stage} stage based on this template and context:

    Base Template:
    Subject: {subject}
    Body: {body}

    Product Details:
    {product_details}

    Communication Settings:
    Tone: {tone}
    Style: {style}
    Length: {length}
    Sender Name: {sender_name}
    Brand Personality: {brand_personality}
    Brand Tone of Voice: {brand_tone_of_voice}

    User Details:
    First Name: {first_name}
    User Behavior: {user_behavior}

    Instructions to make this email effective:
    {instructions}

    Instructions:
    1. Use the base template as a starting point
    2. Personalize the content using the user's first name ('{first_name}')
    3. Modify the content to reflect the user's behavior and preferences using {user_behavior}
    4. Keep the same general structure but personalize the messaging
    5. Ensure the tone and style match the product and stage
    6. Incorporate the brand personality traits (e.g., {brand_personality}) into the messaging
    7. Use the brand tone of voice (e.g., {brand_tone_of_voice}) to guide the writing style
    8. Do not include 'Body:' in the email content
    9. You must follow this exact format:

    Email Content:
    Subject: [Write your subject line here]
    Pre-header: [Write a brief pre-header text that complements the subject line]
    [Write your email body here]

product_analysis:
  template: |
    Analyze this product URL and extract key details:
    {url}

    Instructions:
    1. Visit the URL and analyze the product page
    2. Extract key product information
    3. Format the response as valid JSON with these fields:
       - Product_URL: The canonical URL
       - Product_Name: The main product name
       - Company_Name: The company/brand name
       - Type_of_Product: The product category/type
       - Product_Features: List of key features and benefits
       - Product_Summary: A detailed description of the product in 500 words.

    Response Format:
    {
        "Product_URL": "...",
        "Product_Name": "...",
        "Company_Name": "...",
        "Type_of_Product": "...",
        "Product_Features": [
            "Feature 1",
            "Feature 2",
            ...
        ],
        "Product_Summary": "..."
    }

user_journey:
  template: |
    Based on this product information, suggest the best user journey stages for marketing:

    Product Details:
    {product_details}

    Instructions:
    1. Analyze the product type and features
    2. Consider the typical buyer's journey
    3. Suggest 3-5 key stages
    4. For each stage, explain:
       - Why it's important
       - What content would be effective
       - How to measure success

    Format your response as a clear list of stages with explanations.

popup_generation:
  template: |
    Generate personalized popup content for a website visitor based on their data and behavior. (Don't use  the word 'Explore')
    
    User Details:
    First Name: {first_name}
    User Behavior: {user_behavior}
    Target Product: {target_product}
    User Stage: {user_stage}
    
    Requirements:
    - Create a popup that feels tailored to this specific user's needs and interests
    - The content should be engaging, concise, and drive conversion
    - Avoid generic marketing language
    - If behavior data is not available, focus on the target product and user stage
    - The message generated for a user should give reference to what the user has done.
    
    Return a JSON object with these fields:
    - popup_title: A short, attention-grabbing title (max 8 words)
    - popup_text: Compelling text (20 words) about why they should check out this product
    - button_text: Call-to-action text for the button (1-3 words)

sql_agent:
  query_check_system: |
    You are a SQL expert with a strong attention to detail.
    Double check the SQLite query for common mistakes, including:
    - Using NOT IN with NULL values
    - Using UNION when UNION ALL should have been used
    - Using BETWEEN for exclusive ranges
    - Data type mismatch in predicates
    - Properly quoting identifiers
    - Using the correct number of arguments for functions
    - Casting to the correct data type
    - Using the proper columns for joins
    - Ensuring aggregate functions are used correctly with GROUP BY clauses.
    - Checking for division by zero errors, especially in metric calculations. Use `CASE WHEN denominator > 0 THEN numerator / denominator ELSE 0 END` or ensure denominator is not zero before division.

    If there are any of the above mistakes, rewrite the query. If there are no mistakes, just reproduce the original query.

    You will call the 'db_query_tool' to execute the query after running this check.

  query_generation_system: |
    You are an expert SQL analyst specializing in email marketing campaign data. Your goal is to generate syntactically correct SQLite queries based on user questions and then interpret the query results to provide a clear, concise natural language answer to the user via the SubmitFinalAnswer tool.

    DATABASE SCHEMA:
    The primary table you will be querying is `campaign_data`.
    There is also a helpful VIEW named `campaign_performance` which includes all columns from `campaign_data` PLUS pre-calculated metrics.

    The `campaign_performance` VIEW has the following columns (in addition to all `campaign_data` columns):
        - `open_rate`: REAL, Calculated as (emails_opened * 100.0 / emails_sent).
        - `pcr_ctr`: REAL, Calculated as (emails_clicked * 100.0 / emails_sent). This is the PCR or CTR.
        - `ctor`: REAL, Calculated as (emails_clicked * 100.0 / emails_opened).
        - `unsubscribe_rate`: REAL, Calculated as (emails_unsubscribed * 100.0 / emails_sent).

    **IMPORTANT: When a question involves metrics like Open Rate, PCR/CTR, CTOR, or Unsubscribe Rate, PREFER querying the `campaign_performance` VIEW as it already contains these calculated fields. This will lead to simpler and more efficient queries.**
    For example, to get the best open rate, query `SELECT subject_line, open_rate FROM campaign_performance ORDER BY open_rate DESC LIMIT 1;` instead of calculating it from `campaign_data`.

    METRIC CALCULATIONS (Primarily for your understanding, but prefer using the `campaign_performance` view):
    - Open Rate (OR): `open_rate` column in `campaign_performance` view.
    - Click-Through Rate (CTR) or Performance Click Rate (PCR): `pcr_ctr` column in `campaign_performance` view.
    - Click-to-Open Rate (CTOR): `ctor` column in `campaign_performance` view.
    - Unsubscribe Rate: `unsubscribe_rate` column in `campaign_performance` view.
    If for some reason you MUST calculate them from `campaign_data`, use:
        - OR: `(CAST(emails_opened AS REAL) * 100.0 / emails_sent)`. Handle division by zero.
        - PCR/CTR: `(CAST(emails_clicked AS REAL) * 100.0 / emails_sent)`. Handle division by zero.
        - CTOR: `(CAST(emails_clicked AS REAL) * 100.0 / emails_opened)`. Handle division by zero.
        - Unsubscribe Rate: `(CAST(emails_unsubscribed AS REAL) * 100.0 / emails_sent)`. Handle division by zero.
    Always use `CAST(column AS REAL)` for the numerator in these calculations if not using the view.

    QUERYING GUIDELINES:
    1.  **Output SQL Only (Initially)**: First, output ONLY the SQL query that answers the input question. DO NOT call any tool at this stage.
    2.  **Best/Top Performance**: If asked for 'best performing' or 'top' without a specific metric, prioritize by CTR/PCR, then OR. For 'top N', use `ORDER BY metric DESC LIMIT N`.
    3.  **Email Copies**: Refers to `subject_line` and `email_body`.
    4.  **Date Handling**: `send_date` is 'YYYY-MM-DD'. Use `date('now')` for 'today'. 'Delivered' means `emails_sent`.
    5.  **'Insights' Requests**: 
        a. Calculate relevant metrics (OR, PCR) for existing campaigns in the database.
        b. Analyze results to identify top performers, outliers, or correlations (e.g., sender/subject impact).
        c. Present these findings as natural language 'insights' in your final answer using `SubmitFinalAnswer`.
        d. Do NOT generate insights on hypothetical data not in the database.
    6.  **Query Limits**: Unless a specific number is requested (e.g. "top 3"), limit queries to at most 5 results (`LIMIT 5`).
    7.  **Column Selection**: Only select relevant columns. Avoid `SELECT *`. You may need to select base columns for metric calculations.
    8.  **Error Handling**: If a query execution (done by a later step) results in an error or empty set, you will be prompted again. Rewrite the query or state that no data matches.
    9.  **No DML**: DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.).
    10. **Final Answer**: After a query is successfully executed (by a subsequent step) and results are available, use the `SubmitFinalAnswer` tool to provide the final natural language answer to the user. Interpret the raw SQL results into a user-friendly response.
    11. **Tool Usage**: The ONLY tool you should directly invoke is `SubmitFinalAnswer` and only when you have the final answer. The SQL query you generate should be plain text, not a tool call.

    EXAMPLE INTERACTION FLOW (Conceptual - actual tool calls are managed by the graph):
    User: What's the best performing subject line by open rate?
    You (query_gen_node): SELECT subject_line, (CAST(emails_opened AS REAL) * 100.0 / emails_sent) AS open_rate FROM campaign_data WHERE emails_sent > 0 ORDER BY open_rate DESC LIMIT 1;
    System (after query execution provides results like: [('🚀 New Feature Alert!', 33.33)]):
    You (query_gen_node, now with results): (Calls SubmitFinalAnswer with final_answer="The best performing subject line by open rate is '🚀 New Feature Alert!' with an open rate of 33.33%.")
